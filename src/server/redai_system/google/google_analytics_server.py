#!/usr/bin/env python3
"""
Google Analytics MCP Server

MCP Server cho Google Analytics API sử dụng MCP Python SDK chính thức.
Cung cấp các tools để truy vấn dữ liệu Google Analytics 4 (GA4).

Author: RedAI Team
Date: 2025-01-16
"""

import json
import os
import sys
from typing import Optional, Dict, List

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
sys.path.insert(0, project_root)

# Import MCP SDK chính thức
from mcp.server.fastmcp import FastMCP

# Google Analytics imports
try:
    from google.analytics.data_v1beta import BetaAnalyticsDataClient
    from google.analytics.data_v1beta.types import (
        RunReportRequest,
        RunRealtimeReportRequest,
        RunPivotReportRequest,
        BatchRunReportsRequest,
        GetMetadataRequest,
        CheckCompatibilityRequest,
        Dimension,
        Metric,
        Date<PERSON>ange,
        <PERSON><PERSON>t,
        FilterExpression,
        Filter,
        FilterExpressionList
    )
    from google.oauth2.credentials import Credentials
except ImportError as e:
    print(f"❌ Lỗi import Google Analytics libraries: {e}")
    print("💡 Cài đặt: pip install google-analytics-data")
    sys.exit(1)

import httpx

# Cấu hình server
HTTP_HOST = os.getenv("GOOGLE_ANALYTICS_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_ANALYTICS_HTTP_PORT", "8021"))
HTTP_PATH = os.getenv("GOOGLE_ANALYTICS_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("GOOGLE_ANALYTICS_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GOOGLE_ANALYTICS_SSE_PORT", "8122"))
SSE_PATH = os.getenv("GOOGLE_ANALYTICS_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("GOOGLE_ANALYTICS_TRANSPORT", "http").lower()  # http hoặc sse


def get_http_request():
    """
    Lấy HTTP request object từ FastMCP context
    """
    try:
        import inspect
        frame = inspect.currentframe()
        while frame:
            if 'request' in frame.f_locals:
                return frame.f_locals['request']
            frame = frame.f_back
        return None
    except Exception:
        return None


def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

class GoogleAnalyticsClient:
    """Client để tương tác với Google Analytics Data API"""
    
    def __init__(self):
        self.client = None
    
    def _get_client(self, access_token: str):
        """Tạo Google Analytics client với access token"""
        try:
            credentials = Credentials(token=access_token)
            client = BetaAnalyticsDataClient(credentials=credentials)
            self.client = client
            return client
        except Exception as e:
            raise Exception(f"Lỗi tạo Google Analytics client: {str(e)}")
    
    async def run_report(self, access_token: str, property_id: str, 
                        dimensions: List[str], metrics: List[str],
                        start_date: str = "30daysAgo", end_date: str = "today",
                        limit: int = 100) -> Dict:
        """Chạy report từ Google Analytics"""
        client = self._get_client(access_token)
        
        try:
            # Tạo request
            request = RunReportRequest(
                property=f"properties/{property_id}",
                dimensions=[Dimension(name=dim) for dim in dimensions],
                metrics=[Metric(name=metric) for metric in metrics],
                date_ranges=[DateRange(start_date=start_date, end_date=end_date)],
                limit=limit
            )
            
            # Chạy report
            response = client.run_report(request=request)
            
            # Parse response
            result = {
                'property_id': property_id,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'dimensions': dimensions,
                'metrics': metrics,
                'rows': [],
                'totals': [],
                'row_count': response.row_count
            }
            
            # Parse rows
            for row in response.rows:
                row_data = {}
                
                # Dimensions
                for i, dim_value in enumerate(row.dimension_values):
                    row_data[dimensions[i]] = dim_value.value
                
                # Metrics
                for i, metric_value in enumerate(row.metric_values):
                    row_data[metrics[i]] = metric_value.value
                
                result['rows'].append(row_data)
            
            # Parse totals
            for total in response.totals:
                total_data = {}
                for i, metric_value in enumerate(total.metric_values):
                    total_data[metrics[i]] = metric_value.value
                result['totals'].append(total_data)
            
            return result
            
        except Exception as e:
            raise Exception(f"Google Analytics API error: {str(e)}")
    
    async def get_realtime_report(self, access_token: str, property_id: str,
                                 dimensions: List[str], metrics: List[str],
                                 limit: int = 100) -> Dict:
        """Lấy realtime report từ Google Analytics"""
        client = self._get_client(access_token)
        
        try:
            from google.analytics.data_v1beta.types import RunRealtimeReportRequest
            
            request = RunRealtimeReportRequest(
                property=f"properties/{property_id}",
                dimensions=[Dimension(name=dim) for dim in dimensions],
                metrics=[Metric(name=metric) for metric in metrics],
                limit=limit
            )
            
            response = client.run_realtime_report(request=request)
            
            result = {
                'property_id': property_id,
                'report_type': 'realtime',
                'dimensions': dimensions,
                'metrics': metrics,
                'rows': [],
                'totals': [],
                'row_count': response.row_count
            }
            
            # Parse rows
            for row in response.rows:
                row_data = {}
                
                # Dimensions
                for i, dim_value in enumerate(row.dimension_values):
                    row_data[dimensions[i]] = dim_value.value
                
                # Metrics
                for i, metric_value in enumerate(row.metric_values):
                    row_data[metrics[i]] = metric_value.value
                
                result['rows'].append(row_data)
            
            # Parse totals
            for total in response.totals:
                total_data = {}
                for i, metric_value in enumerate(total.metric_values):
                    total_data[metrics[i]] = metric_value.value
                result['totals'].append(total_data)
            
            return result
            
        except Exception as e:
            raise Exception(f"Google Analytics Realtime API error: {str(e)}")

    async def batch_run_reports(self, access_token: str, property_id: str, reports_json: str) -> Dict:
        """Chạy nhiều reports cùng lúc"""
        client = self._get_client(access_token)

        try:
            from google.analytics.data_v1beta.types import BatchRunReportsRequest
            import json

            reports_data = json.loads(reports_json)

            # Tạo requests từ JSON
            requests = []
            for report_data in reports_data:
                request = RunReportRequest(
                    property=f"properties/{property_id}",
                    dimensions=[Dimension(name=dim) for dim in report_data.get('dimensions', [])],
                    metrics=[Metric(name=metric) for metric in report_data.get('metrics', [])],
                    date_ranges=[DateRange(
                        start_date=report_data.get('start_date', '30daysAgo'),
                        end_date=report_data.get('end_date', 'today')
                    )],
                    limit=report_data.get('limit', 100)
                )
                requests.append(request)

            batch_request = BatchRunReportsRequest(
                property=f"properties/{property_id}",
                requests=requests
            )

            response = client.batch_run_reports(request=batch_request)

            results = []
            for i, report in enumerate(response.reports):
                result = {
                    'report_index': i,
                    'rows': [],
                    'totals': [],
                    'row_count': report.row_count
                }

                # Parse rows
                for row in report.rows:
                    row_data = {}
                    for j, dim_value in enumerate(row.dimension_values):
                        row_data[reports_data[i]['dimensions'][j]] = dim_value.value
                    for j, metric_value in enumerate(row.metric_values):
                        row_data[reports_data[i]['metrics'][j]] = metric_value.value
                    result['rows'].append(row_data)

                results.append(result)

            return {
                'property_id': property_id,
                'reports': results,
                'total_reports': len(results)
            }

        except Exception as e:
            raise Exception(f"Google Analytics Batch API error: {str(e)}")

    async def get_metadata(self, access_token: str, property_id: str) -> Dict:
        """Lấy metadata về dimensions và metrics"""
        client = self._get_client(access_token)

        try:
            from google.analytics.data_v1beta.types import GetMetadataRequest

            request = GetMetadataRequest(name=f"properties/{property_id}/metadata")
            response = client.get_metadata(request=request)

            dimensions = []
            for dimension in response.dimensions:
                dimensions.append({
                    'api_name': dimension.api_name,
                    'ui_name': dimension.ui_name,
                    'description': dimension.description,
                    'category': dimension.category,
                    'custom_definition': dimension.custom_definition
                })

            metrics = []
            for metric in response.metrics:
                metrics.append({
                    'api_name': metric.api_name,
                    'ui_name': metric.ui_name,
                    'description': metric.description,
                    'type': metric.type_.name,
                    'expression': metric.expression,
                    'custom_definition': metric.custom_definition
                })

            return {
                'property_id': property_id,
                'dimensions': dimensions,
                'metrics': metrics,
                'dimensions_count': len(dimensions),
                'metrics_count': len(metrics)
            }

        except Exception as e:
            raise Exception(f"Google Analytics Metadata API error: {str(e)}")

    async def run_pivot_report(self, access_token: str, property_id: str,
                              dimensions: List[str], metrics: List[str],
                              pivots_json: str, start_date: str = "30daysAgo",
                              end_date: str = "today") -> Dict:
        """Chạy pivot report từ Google Analytics"""
        client = self._get_client(access_token)

        try:
            from google.analytics.data_v1beta.types import RunPivotReportRequest, Pivot
            import json

            pivots_data = json.loads(pivots_json)

            # Tạo pivot objects
            pivots = []
            for pivot_data in pivots_data:
                pivot = Pivot(
                    field_names=pivot_data.get('field_names', []),
                    limit=pivot_data.get('limit', 100)
                )
                pivots.append(pivot)

            request = RunPivotReportRequest(
                property=f"properties/{property_id}",
                dimensions=[Dimension(name=dim) for dim in dimensions],
                metrics=[Metric(name=metric) for metric in metrics],
                date_ranges=[DateRange(start_date=start_date, end_date=end_date)],
                pivots=pivots
            )

            response = client.run_pivot_report(request=request)

            result = {
                'property_id': property_id,
                'report_type': 'pivot',
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'dimensions': dimensions,
                'metrics': metrics,
                'pivot_headers': [],
                'rows': [],
                'row_count': response.row_count
            }

            # Parse pivot headers
            for header in response.pivot_headers:
                result['pivot_headers'].append({
                    'pivot_dimension_headers': [h.dimension_values for h in header.pivot_dimension_headers],
                    'total_dimension_count': header.total_dimension_count
                })

            # Parse rows
            for row in response.rows:
                row_data = {
                    'dimension_values': [dv.value for dv in row.dimension_values],
                    'metric_values': [mv.value for mv in row.metric_values]
                }
                result['rows'].append(row_data)

            return result

        except Exception as e:
            raise Exception(f"Google Analytics Pivot API error: {str(e)}")

# Khởi tạo Google Analytics client
analytics_client = GoogleAnalyticsClient()

# Khởi tạo MCP server
mcp = FastMCP("Google-Analytics-Server")

# FastMCP tự động handle tools/list từ các @mcp.tool() decorators
# Không cần implement list_tools manual

@mcp.tool(description="Chạy report từ Google Analytics 4")
async def run_report_tool(
    ctx,
    property_id: str,
    dimensions: str,
    metrics: str,
    start_date: str = "30daysAgo",
    end_date: str = "today",
    limit: int = 100
) -> str:
    """
    Chạy report từ Google Analytics 4
    
    Args:
        property_id: GA4 Property ID
        dimensions: Danh sách dimensions cách nhau bởi dấu phẩy
        metrics: Danh sách metrics cách nhau bởi dấu phẩy
        start_date: Ngày bắt đầu
        end_date: Ngày kết thúc
        limit: Số lượng rows trả về
    
    Returns:
        JSON string chứa dữ liệu report
    """
    try:
        # Extract access token từ request headers
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"
        
        # Parse dimensions và metrics
        dimensions_list = [dim.strip() for dim in dimensions.split(',')]
        metrics_list = [metric.strip() for metric in metrics.split(',')]
        
        await ctx.info(f"📊 Chạy GA4 report cho property: {property_id}")
        await ctx.info(f"📅 Thời gian: {start_date} đến {end_date}")
        await ctx.info(f"📏 Dimensions: {dimensions_list}")
        await ctx.info(f"📈 Metrics: {metrics_list}")
        
        result = await analytics_client.run_report(
            access_token, property_id, dimensions_list, metrics_list,
            start_date, end_date, limit
        )
        
        # Log thông tin cơ bản
        row_count = result.get('row_count', 0)
        await ctx.info(f"✅ Trả về {row_count} rows")
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi chạy report: {str(e)}")
        return f"❌ Lỗi chạy report: {str(e)}"

@mcp.tool(description="Lấy realtime report từ Google Analytics 4")
async def get_realtime_report_tool(
    ctx,
    property_id: str,
    dimensions: str,
    metrics: str,
    limit: int = 100
) -> str:
    """
    Lấy realtime report từ Google Analytics 4
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        dimensions_list = [dim.strip() for dim in dimensions.split(',')]
        metrics_list = [metric.strip() for metric in metrics.split(',')]

        await ctx.info(f"⚡ Lấy realtime report cho property: {property_id}")

        result = await analytics_client.get_realtime_report(
            access_token, property_id, dimensions_list, metrics_list, limit
        )

        row_count = result.get('row_count', 0)
        await ctx.info(f"✅ Trả về {row_count} realtime rows")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy realtime report: {str(e)}")
        return f"❌ Lỗi lấy realtime report: {str(e)}"


@mcp.tool(description="Chạy pivot report từ Google Analytics 4")
async def run_pivot_report_tool(
    ctx,
    property_id: str,
    dimensions: str,
    metrics: str,
    pivots: str,
    start_date: str = "30daysAgo",
    end_date: str = "today"
) -> str:
    """
    Chạy pivot report từ Google Analytics 4
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        # Parse dimensions và metrics
        dimensions_list = [dim.strip() for dim in dimensions.split(',')]
        metrics_list = [metric.strip() for metric in metrics.split(',')]

        await ctx.info(f"📊 Chạy pivot report cho property: {property_id}")
        await ctx.info(f"📏 Dimensions: {dimensions_list}")
        await ctx.info(f"📈 Metrics: {metrics_list}")

        result = await analytics_client.run_pivot_report(
            access_token, property_id, dimensions_list, metrics_list,
            pivots, start_date, end_date
        )

        row_count = result.get('row_count', 0)
        await ctx.info(f"✅ Trả về {row_count} pivot rows")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi chạy pivot report: {str(e)}")
        return f"❌ Lỗi chạy pivot report: {str(e)}"


@mcp.tool(description="Lấy danh sách trang phổ biến nhất")
async def get_popular_pages_tool(
    ctx,
    property_id: str,
    start_date: str = "7daysAgo",
    end_date: str = "today",
    limit: int = 20
) -> str:
    """
    Lấy danh sách trang phổ biến nhất
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📄 Lấy popular pages cho property: {property_id}")

        result = await analytics_client.run_report(
            access_token, property_id,
            ['pagePath', 'pageTitle'],
            ['screenPageViews', 'sessions', 'users'],
            start_date, end_date, limit
        )

        await ctx.info(f"✅ Trả về {len(result.get('rows', []))} popular pages")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy popular pages: {str(e)}")
        return f"❌ Lỗi lấy popular pages: {str(e)}"

@mcp.tool(description="Lấy thông tin traffic sources")
async def get_traffic_sources_tool(
    ctx,
    property_id: str,
    start_date: str = "30daysAgo",
    end_date: str = "today",
    limit: int = 20
) -> str:
    """
    Lấy thông tin traffic sources
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🚦 Lấy traffic sources cho property: {property_id}")

        result = await analytics_client.run_report(
            access_token, property_id,
            ['sessionSource', 'sessionMedium', 'sessionCampaign'],
            ['sessions', 'users', 'newUsers', 'bounceRate'],
            start_date, end_date, limit
        )

        await ctx.info(f"✅ Trả về {len(result.get('rows', []))} traffic sources")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy traffic sources: {str(e)}")
        return f"❌ Lỗi lấy traffic sources: {str(e)}"

@mcp.tool(description="Chạy nhiều reports cùng lúc (batch)")
async def batch_run_reports_tool(
    ctx,
    property_id: str,
    reports: str
) -> str:
    """
    Chạy nhiều reports cùng lúc
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📊 Chạy batch reports cho property: {property_id}")

        result = await analytics_client.batch_run_reports(access_token, property_id, reports)

        reports_count = result.get('total_reports', 0)
        await ctx.info(f"✅ Hoàn thành {reports_count} reports")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi chạy batch reports: {str(e)}")
        return f"❌ Lỗi chạy batch reports: {str(e)}"

@mcp.tool(description="Lấy metadata về dimensions và metrics có sẵn")
async def get_metadata_tool(
    ctx,
    property_id: str
) -> str:
    """
    Lấy metadata về dimensions và metrics có sẵn
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📋 Lấy metadata cho property: {property_id}")

        result = await analytics_client.get_metadata(access_token, property_id)

        dimensions_count = result.get('dimensions_count', 0)
        metrics_count = result.get('metrics_count', 0)
        await ctx.info(f"✅ Trả về {dimensions_count} dimensions và {metrics_count} metrics")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy metadata: {str(e)}")
        return f"❌ Lỗi lấy metadata: {str(e)}"

@mcp.tool(description="Kiểm tra compatibility của dimensions và metrics")
async def check_compatibility_tool(
    ctx,
    property_id: str,
    dimensions: str,
    metrics: str
) -> str:
    """
    Kiểm tra compatibility của dimensions và metrics
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        # Parse dimensions và metrics
        dimensions_list = [dim.strip() for dim in dimensions.split(',')]
        metrics_list = [metric.strip() for metric in metrics.split(',')]

        await ctx.info(f"🔍 Kiểm tra compatibility cho property: {property_id}")
        await ctx.info(f"📏 Dimensions: {dimensions_list}")
        await ctx.info(f"📈 Metrics: {metrics_list}")

        # Sử dụng checkCompatibility API
        client = analytics_client._get_client(access_token)

        from google.analytics.data_v1beta.types import CheckCompatibilityRequest

        request = CheckCompatibilityRequest(
            property=f"properties/{property_id}",
            dimensions=[Dimension(name=dim) for dim in dimensions_list],
            metrics=[Metric(name=metric) for metric in metrics_list]
        )

        response = client.check_compatibility(request=request)

        result = {
            'property_id': property_id,
            'dimensions': dimensions_list,
            'metrics': metrics_list,
            'compatible': True,
            'dimension_compatibilities': [],
            'metric_compatibilities': []
        }

        # Parse dimension compatibilities
        for dim_compat in response.dimension_compatibilities:
            result['dimension_compatibilities'].append({
                'dimension_name': dim_compat.dimension_metadata.api_name,
                'compatibility': dim_compat.compatibility.name
            })

        # Parse metric compatibilities
        for metric_compat in response.metric_compatibilities:
            result['metric_compatibilities'].append({
                'metric_name': metric_compat.metric_metadata.api_name,
                'compatibility': metric_compat.compatibility.name
            })

        await ctx.info(f"✅ Compatibility check hoàn thành")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi kiểm tra compatibility: {str(e)}")
        return f"❌ Lỗi kiểm tra compatibility: {str(e)}"

# Health check endpoint
@mcp.tool(description="Health check cho Google Analytics server")
async def health_check() -> str:
    """Health check endpoint"""
    return json.dumps({
        "status": "healthy",
        "service": "Google Analytics MCP Server",
        "port": HTTP_PORT
    })

if __name__ == "__main__":
    print("🚀 Starting Google Analytics MCP Server")
    print("=" * 50)

    # Sử dụng transport mặc định của MCP SDK
    if TRANSPORT_TYPE == "sse":
        print(f"📡 Transport: SSE")
        print(f"🌐 SSE endpoint: http://{SSE_HOST}:{SSE_PORT}{SSE_PATH}")
        print(f"🏥 Health check: http://{SSE_HOST}:{SSE_PORT}/health")
        print("⚠️  Note: SSE transport fallback - Streamable HTTP transport preferred")

        # Sử dụng SSE transport
        try:
            mcp.run(transport="sse", host=SSE_HOST, port=SSE_PORT, mount_path=SSE_PATH)
        except Exception as e:
            print(f"❌ SSE transport failed: {e}")
            print("🔄 Falling back to Streamable HTTP transport...")
            TRANSPORT_TYPE = "http"

    if TRANSPORT_TYPE == "http":
        print(f"📡 Transport: Streamable HTTP")
        print(f"🌐 HTTP endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print(f"🏥 Health check: http://{HTTP_HOST}:{HTTP_PORT}/health")

        # Sử dụng Streamable HTTP transport (mặc định của MCP SDK)
        mcp.run(transport="streamable-http", host=HTTP_HOST, port=HTTP_PORT, mount_path=HTTP_PATH)
