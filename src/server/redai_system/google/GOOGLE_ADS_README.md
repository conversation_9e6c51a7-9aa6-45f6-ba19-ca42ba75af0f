# Google Ads MCP Server

MCP Server cho Google Ads API sử dụng **MCP Python SDK chính thức**.

## 🎯 Tính năng

### ✅ Đã được sửa theo chuẩn MCP Python SDK:
- **Sử dụng `mcp.server.fastmcp.FastMCP`** thay vì `fastmcp.FastMCP`
- **Xóa `@mcp._mcp_list_tools()`** - FastMCP tự động handle tools
- **Xóa manual `list_tools()`** - Không cần thiết với MCP SDK
- **Sử dụng `@mcp.tool()` decorators** - C<PERSON>ch chuẩn của MCP SDK
- **Streamable HTTP transport** - Transport mặc định của MCP SDK
- **SSE fallback** - Tự động fallback nếu HTTP fail

### 🚀 Chức năng Google Ads:
- **Campaign Management**: <PERSON><PERSON><PERSON>, đ<PERSON><PERSON>, cập nhật, xóa campaigns
- **Ad Group Management**: <PERSON><PERSON><PERSON>, đ<PERSON><PERSON>, c<PERSON><PERSON> nh<PERSON>, xóa ad groups
- **Keyword Management**: <PERSON>ạ<PERSON>, đọc, xóa keywords + keyword research
- **Ad Management**: Tạo Responsive Search Ads, đọc, tạm dừng ads
- **Performance Reporting**: Campaign performance metrics
- **Bidding Strategies**: Quản lý bidding strategies
- **Optimization**: Google Ads recommendations
- **Extensions**: Sitelinks, Call extensions
- **Advanced Campaigns**: Performance Max, Smart campaigns

## 📋 Yêu cầu

### Dependencies:
```bash
# MCP Python SDK chính thức
mcp>=1.0.0

# Google Ads API
google-ads>=23.1.0
PyYAML>=6.0

# Google OAuth2
google-auth>=2.20.0
```

### Google Ads API Credentials:
1. **Developer Token**: Từ Google Ads API Center
2. **OAuth2 Credentials**: Từ Google Cloud Console
3. **Customer ID**: Google Ads account ID

## ⚙️ Cấu hình

### Environment Variables (.env):
```bash
# Google Ads API Configuration
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token_here
GOOGLE_ADS_LOGIN_CUSTOMER_ID=your_login_customer_id_here

# OAuth2 Configuration (optional)
GOOGLE_OAUTH2_CLIENT_ID=your_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_client_secret_here
GOOGLE_OAUTH2_REDIRECT_URI=http://localhost:8080/oauth2callback

# Transport Configuration
GOOGLE_ADS_TRANSPORT=http  # "http" hoặc "sse"

# HTTP Transport (Streamable HTTP - preferred)
GOOGLE_ADS_HTTP_HOST=0.0.0.0
GOOGLE_ADS_HTTP_PORT=8020
GOOGLE_ADS_HTTP_PATH=/mcp

# SSE Transport (fallback)
GOOGLE_ADS_SSE_HOST=0.0.0.0
GOOGLE_ADS_SSE_PORT=8120
GOOGLE_ADS_SSE_PATH=/sse
```

## 🧪 Testing

### 1. Test Configuration:
```bash
python src/server/redai_system/google/test_google_ads_config.py
```

### 2. Run Server:
```bash
# Development
python src/server/redai_system/google/google_ads_server.py

# Docker
docker-compose up google-ads
```

### 3. Test với MCP Inspector:
```bash
# Nếu có MCP CLI tools
mcp dev src/server/redai_system/google/google_ads_server.py
```

## 🔧 Sử dụng

### Authentication:
Server yêu cầu **Bearer token** trong Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_GOOGLE_OAUTH2_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "method": "tools/call",
       "params": {
         "name": "get_campaigns_tool",
         "arguments": {"customer_id": "1234567890"}
       }
     }' \
     http://localhost:8020/mcp
```

### Available Tools:

#### 📊 Campaign Tools:
- `get_campaigns_tool` - Lấy danh sách campaigns
- `create_campaign_tool` - Tạo campaign mới
- `get_campaign_performance_tool` - Performance report

#### 📁 Ad Group Tools:
- `get_ad_groups_tool` - Lấy danh sách ad groups
- `create_ad_group_tool` - Tạo ad group mới

#### 🔑 Keyword Tools:
- `get_keywords_tool` - Lấy danh sách keywords
- `create_keyword_tool` - Tạo keyword mới
- `get_keyword_ideas_tool` - Keyword research

#### 📝 Ad Tools:
- `get_ads_tool` - Lấy danh sách ads
- `create_responsive_search_ad_tool` - Tạo RSA

#### 🎯 Bidding Tools:
- `get_bidding_strategies_tool` - Lấy bidding strategies
- `create_target_cpa_bidding_strategy_tool` - Tạo Target CPA strategy

#### 💡 Optimization Tools:
- `get_recommendations_tool` - Google Ads recommendations

#### 🔄 Update/Delete Tools:
- `update_campaign_tool` - Cập nhật campaign
- `remove_campaign_tool` - Xóa campaign
- `pause_ad_tool` - Tạm dừng ad

#### 🔗 Extension Tools:
- `add_sitelinks_tool` - Thêm sitelink extensions
- `add_call_extensions_tool` - Thêm call extensions

#### 🚀 Advanced Campaign Tools:
- `add_performance_max_campaign_tool` - Tạo Performance Max campaign
- `add_smart_campaign_tool` - Tạo Smart campaign

## 🐳 Docker

### docker-compose.yml:
```yaml
google-ads:
  build:
    context: .
    dockerfile: src/server/redai_system/google/Dockerfile
  container_name: google-ads-server
  ports:
    - "8020:8020"  # HTTP
    - "8120:8120"  # SSE fallback
  environment:
    - GOOGLE_ADS_TRANSPORT=http
    - GOOGLE_ADS_DEVELOPER_TOKEN=${GOOGLE_ADS_DEVELOPER_TOKEN}
    - GOOGLE_ADS_LOGIN_CUSTOMER_ID=${GOOGLE_ADS_LOGIN_CUSTOMER_ID}
```

## 📚 Tài liệu tham khảo

- **MCP Python SDK**: https://github.com/modelcontextprotocol/python-sdk
- **Google Ads API**: https://developers.google.com/google-ads/api/docs
- **Google Ads Query Language (GAQL)**: https://developers.google.com/google-ads/api/docs/query/overview
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 🔍 Troubleshooting

### Import Errors:
```bash
❌ ImportError: No module named 'mcp.server.fastmcp'
✅ Solution: pip install mcp>=1.0.0
```

### Google Ads API Errors:
```bash
❌ GoogleAdsException: AUTHENTICATION_ERROR
✅ Solution: Kiểm tra Developer Token và OAuth2 credentials
```

### Transport Errors:
```bash
❌ SSE transport failed
✅ Solution: Server tự động fallback sang HTTP transport
```

## 🎉 Kết luận

Server đã được cập nhật để tuân thủ **MCP Python SDK chính thức**:
- ✅ Sử dụng `mcp.server.fastmcp.FastMCP`
- ✅ Xóa manual tools listing
- ✅ Sử dụng Streamable HTTP transport
- ✅ Hỗ trợ SSE fallback
- ✅ Đầy đủ chức năng Google Ads API
