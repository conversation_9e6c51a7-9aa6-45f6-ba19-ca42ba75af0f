#!/usr/bin/env python3
"""
Google Drive MCP Server sử dụng FastMCP và Google Drive API v3

Server này cung cấp các MCP tools để tương tác với Google Drive API,
bao gồm quản lý files, folders, permissions và sharing.

Tính năng:
- Quản lý files và folders (create, read, update, delete)
- Upload và download files
- Copy và move files
- Quản lý permissions và sharing
- Search files và folders
- Metadata management

Transport: Streamable HTTP
Authentication: Google OAuth2 với access token từ client headers
"""

import io
import json
import os
from typing import Dict, List, Optional

from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request
from google.oauth2 import credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseUpload
from datetime import datetime

# Google Drive API scopes
SCOPES = [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/drive.file',
    'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/drive.metadata',
    'https://www.googleapis.com/auth/drive.metadata.readonly'
]

# Cấu hình server
HTTP_HOST = os.getenv("GOOGLE_DRIVE_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_DRIVE_HTTP_PORT", "8017"))
HTTP_PATH = os.getenv("GOOGLE_DRIVE_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("GOOGLE_DRIVE_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GOOGLE_DRIVE_SSE_PORT", "8117"))
SSE_PATH = os.getenv("GOOGLE_DRIVE_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("GOOGLE_DRIVE_TRANSPORT", "http").lower()  # http hoặc sse


def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers
    
    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None


class GoogleDriveClient:
    """Client để tương tác với Google Drive API"""

    def __init__(self):
        # Không khởi tạo service cố định, sẽ tạo mới mỗi lần với token từ request
        pass

    def _create_service_with_token(self, access_token: str):
        """Tạo Google Drive service với access token từ client"""
        try:
            # Tạo credentials từ access token (sử dụng cách mới thay vì deprecated AccessTokenCredentials)
            from google.oauth2.credentials import Credentials

            creds = Credentials(token=access_token)

            # Tạo service với credentials
            service = build('drive', 'v3', credentials=creds)
            return service

        except Exception as e:
            raise Exception(f"Lỗi tạo Google Drive service với token: {str(e)}")

    def _get_service(self):
        """Lấy service với token từ request headers"""
        token = extract_bearer_token_from_request()
        if not token:
            raise Exception(
                "Không tìm thấy Bearer token trong request headers. Vui lòng cung cấp Google OAuth2 access token.")

        return self._create_service_with_token(token)

    async def list_files(self, query: str = None, page_size: int = 10,
                         fields: str = None, order_by: str = None) -> Dict:
        """List files trong Drive"""
        service = self._get_service()

        try:
            # Chuẩn bị parameters
            params = {
                'pageSize': page_size,
                'fields': fields or 'nextPageToken, files(id, name, mimeType, size, createdTime, modifiedTime, parents, webViewLink)'
            }

            if query:
                params['q'] = query
            if order_by:
                params['orderBy'] = order_by

            result = service.files().list(**params).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi list files: {str(e)}")

    async def get_file(self, file_id: str, fields: str = None) -> Dict:
        """Lấy thông tin file"""
        service = self._get_service()

        try:
            params = {'fileId': file_id}
            if fields:
                params['fields'] = fields
            else:
                params[
                    'fields'] = 'id, name, mimeType, size, createdTime, modifiedTime, parents, webViewLink, webContentLink, permissions'

            result = service.files().get(**params).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi get file: {str(e)}")

    async def create_file(self, name: str, mime_type: str = None,
                          parent_id: str = None, content: str = None) -> Dict:
        """Tạo file mới"""
        service = self._get_service()

        try:
            # Metadata cho file
            file_metadata = {'name': name}

            if parent_id:
                file_metadata['parents'] = [parent_id]

            # Tạo file
            if content:
                # Tạo file với content
                media = MediaIoBaseUpload(
                    io.BytesIO(content.encode('utf-8')),
                    mimetype=mime_type or 'text/plain'
                )
                result = service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id, name, mimeType, webViewLink'
                ).execute()
            else:
                # Tạo file trống hoặc folder
                if mime_type:
                    file_metadata['mimeType'] = mime_type

                result = service.files().create(
                    body=file_metadata,
                    fields='id, name, mimeType, webViewLink'
                ).execute()

            return result
        except HttpError as e:
            raise Exception(f"Lỗi create file: {str(e)}")

    async def update_file(self, file_id: str, name: str = None,
                          content: str = None, mime_type: str = None) -> Dict:
        """Update file"""
        service = self._get_service()

        try:
            # Metadata update
            file_metadata = {}
            if name:
                file_metadata['name'] = name

            params = {
                'fileId': file_id,
                'body': file_metadata,
                'fields': 'id, name, mimeType, modifiedTime, webViewLink'
            }

            # Update content nếu có
            if content:
                media = MediaIoBaseUpload(
                    io.BytesIO(content.encode('utf-8')),
                    mimetype=mime_type or 'text/plain'
                )
                params['media_body'] = media

            result = service.files().update(**params).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi update file: {str(e)}")

    async def delete_file(self, file_id: str) -> Dict:
        """Xóa file"""
        service = self._get_service()

        try:
            service.files().delete(fileId=file_id).execute()
            return {"success": True, "message": f"File {file_id} đã được xóa"}
        except HttpError as e:
            raise Exception(f"Lỗi delete file: {str(e)}")

    async def copy_file(self, file_id: str, name: str, parent_id: str = None) -> Dict:
        """Copy file"""
        service = self._get_service()

        try:
            file_metadata = {'name': name}
            if parent_id:
                file_metadata['parents'] = [parent_id]

            result = service.files().copy(
                fileId=file_id,
                body=file_metadata,
                fields='id, name, mimeType, webViewLink'
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi copy file: {str(e)}")


# Khởi tạo Google Drive client
drive_client = GoogleDriveClient()

# Khởi tạo MCP server
mcp = FastMCP("Google-Drive-Server")

@mcp.tool()
async def list_files_tool(
        ctx,
        query: str = "",
        page_size: int = 10,
        order_by: str = "modifiedTime desc"
) -> str:
    """
    List files trong Google Drive

    Args:
        query: Query string để filter files (ví dụ: "name contains 'test'")
        page_size: Số lượng files trả về (mặc định 10)
        order_by: Sắp xếp theo (mặc định "modifiedTime desc")

    Returns:
        JSON string chứa danh sách files
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📁 List files với query: '{query}', page_size: {page_size}")

        result = await drive_client.list_files(
            query=query if query else None,
            page_size=page_size,
            order_by=order_by
        )

        files = result.get('files', [])
        await ctx.info(f"✅ Tìm thấy {len(files)} files")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi list files: {str(e)}")
        return f"❌ Lỗi list files: {str(e)}"


@mcp.tool()
async def get_file_info_tool(
        ctx,
        file_id: str,
        fields: str = ""
) -> str:
    """
    Lấy thông tin chi tiết về file

    Args:
        file_id: ID của file
        fields: Các fields cần lấy (tùy chọn)

    Returns:
        JSON string chứa thông tin file
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📄 Lấy thông tin file: {file_id}")

        result = await drive_client.get_file(
            file_id=file_id,
            fields=fields if fields else None
        )

        file_name = result.get('name', 'Unknown')
        file_type = result.get('mimeType', 'Unknown')
        await ctx.info(f"✅ File info: {file_name} ({file_type})")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi get file info: {str(e)}")
        return f"❌ Lỗi get file info: {str(e)}"


@mcp.tool(description="Tạo file mới trong Google Drive")
async def create_file_tool(
        ctx,
        name: str,
        mime_type: str = "text/plain",
        parent_id: str = "",
        content: str = ""
) -> str:
    """
    Tạo file mới trong Google Drive

    Args:
        name: Tên file
        mime_type: MIME type của file (mặc định "text/plain")
        parent_id: ID của folder cha (tùy chọn)
        content: Nội dung file (tùy chọn)

    Returns:
        JSON string chứa thông tin file đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📝 Tạo file: {name} ({mime_type})")
        if parent_id:
            await ctx.info(f"📁 Parent folder: {parent_id}")
        if content:
            await ctx.info(f"📄 Content length: {len(content)} characters")

        result = await drive_client.create_file(
            name=name,
            mime_type=mime_type if mime_type else None,
            parent_id=parent_id if parent_id else None,
            content=content if content else None
        )

        file_id = result.get('id', 'Unknown')
        web_link = result.get('webViewLink', 'Unknown')
        await ctx.info(f"✅ Đã tạo file thành công - ID: {file_id}")
        await ctx.info(f"🔗 Link: {web_link}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo file: {str(e)}")
        return f"❌ Lỗi tạo file: {str(e)}"


@mcp.tool()
async def create_folder_tool(
        ctx,
        name: str,
        parent_id: str = ""
) -> str:
    """
    Tạo folder mới trong Google Drive

    Args:
        name: Tên folder
        parent_id: ID của folder cha (tùy chọn)

    Returns:
        JSON string chứa thông tin folder đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📁 Tạo folder: {name}")
        if parent_id:
            await ctx.info(f"📁 Parent folder: {parent_id}")

        result = await drive_client.create_file(
            name=name,
            mime_type="application/vnd.google-apps.folder",
            parent_id=parent_id if parent_id else None
        )

        folder_id = result.get('id', 'Unknown')
        web_link = result.get('webViewLink', 'Unknown')
        await ctx.info(f"✅ Đã tạo folder thành công - ID: {folder_id}")
        await ctx.info(f"🔗 Link: {web_link}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo folder: {str(e)}")
        return f"❌ Lỗi tạo folder: {str(e)}"


@mcp.tool()
async def update_file_tool(
        ctx,
        file_id: str,
        name: str = "",
        content: str = "",
        mime_type: str = ""
) -> str:
    """
    Cập nhật file trong Google Drive

    Args:
        file_id: ID của file cần update
        name: Tên mới của file (tùy chọn)
        content: Nội dung mới của file (tùy chọn)
        mime_type: MIME type mới (tùy chọn)

    Returns:
        JSON string chứa thông tin file đã update
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"✏️ Update file: {file_id}")
        if name:
            await ctx.info(f"📝 New name: {name}")
        if content:
            await ctx.info(f"📄 New content length: {len(content)} characters")

        result = await drive_client.update_file(
            file_id=file_id,
            name=name if name else None,
            content=content if content else None,
            mime_type=mime_type if mime_type else None
        )

        file_name = result.get('name', 'Unknown')
        web_link = result.get('webViewLink', 'Unknown')
        await ctx.info(f"✅ Đã update file thành công: {file_name}")
        await ctx.info(f"🔗 Link: {web_link}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi update file: {str(e)}")
        return f"❌ Lỗi update file: {str(e)}"


@mcp.tool()
async def delete_file_tool(
        ctx,
        file_id: str
) -> str:
    """
    Xóa file khỏi Google Drive

    Args:
        file_id: ID của file cần xóa

    Returns:
        JSON string chứa kết quả xóa
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🗑️ Xóa file: {file_id}")

        result = await drive_client.delete_file(file_id)

        await ctx.info(f"✅ Đã xóa file thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi xóa file: {str(e)}")
        return f"❌ Lỗi xóa file: {str(e)}"


@mcp.tool()
async def copy_file_tool(
        ctx,
        file_id: str,
        name: str,
        parent_id: str = ""
) -> str:
    """
    Copy file trong Google Drive

    Args:
        file_id: ID của file cần copy
        name: Tên của file copy
        parent_id: ID của folder đích (tùy chọn)

    Returns:
        JSON string chứa thông tin file đã copy
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📋 Copy file {file_id} -> {name}")
        if parent_id:
            await ctx.info(f"📁 Destination folder: {parent_id}")

        result = await drive_client.copy_file(
            file_id=file_id,
            name=name,
            parent_id=parent_id if parent_id else None
        )

        new_file_id = result.get('id', 'Unknown')
        web_link = result.get('webViewLink', 'Unknown')
        await ctx.info(f"✅ Đã copy file thành công - New ID: {new_file_id}")
        await ctx.info(f"🔗 Link: {web_link}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi copy file: {str(e)}")
        return f"❌ Lỗi copy file: {str(e)}"


@mcp.tool()
async def search_files_tool(
        ctx,
        search_term: str,
        file_type: str = "",
        in_folder: str = "",
        page_size: int = 20
) -> str:
    """
    Tìm kiếm files trong Google Drive

    Args:
        search_term: Từ khóa tìm kiếm
        file_type: Loại file (ví dụ: "document", "spreadsheet", "folder")
        in_folder: ID của folder để tìm kiếm trong đó (tùy chọn)
        page_size: Số lượng kết quả trả về (mặc định 20)

    Returns:
        JSON string chứa kết quả tìm kiếm
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        # Xây dựng query
        query_parts = []

        # Tìm kiếm theo tên
        if search_term:
            query_parts.append(f"name contains '{search_term}'")

        # Filter theo loại file
        if file_type:
            if file_type.lower() == "folder":
                query_parts.append("mimeType = 'application/vnd.google-apps.folder'")
            elif file_type.lower() == "document":
                query_parts.append("mimeType = 'application/vnd.google-apps.document'")
            elif file_type.lower() == "spreadsheet":
                query_parts.append("mimeType = 'application/vnd.google-apps.spreadsheet'")
            elif file_type.lower() == "presentation":
                query_parts.append("mimeType = 'application/vnd.google-apps.presentation'")

        # Tìm kiếm trong folder cụ thể
        if in_folder:
            query_parts.append(f"'{in_folder}' in parents")

        # Loại bỏ files trong trash
        query_parts.append("trashed = false")

        query = " and ".join(query_parts)

        await ctx.info(f"🔍 Tìm kiếm với query: {query}")

        result = await drive_client.list_files(
            query=query,
            page_size=page_size,
            order_by="modifiedTime desc"
        )

        files = result.get('files', [])
        await ctx.info(f"✅ Tìm thấy {len(files)} files")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi search files: {str(e)}")
        return f"❌ Lỗi search files: {str(e)}"


@mcp.tool(description="Health check endpoint")
async def health_check(ctx) -> str:
    """
    Health check endpoint để kiểm tra trạng thái server

    Returns:
        JSON string chứa thông tin health status
    """
    try:
        await ctx.info("🏥 Health check")

        health_status = {
            "status": "healthy",
            "server": "Google Drive MCP Server",
            "version": "1.0.0",
            "api": "Google Drive API v3",
            "transport": "FastMCP Streamable HTTP",
            "timestamp": datetime.now().isoformat(),
            "scopes": SCOPES,
            "endpoints": {
                "http": f"http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}",
                "sse_fallback": f"http://{SSE_HOST}:{SSE_PORT}{SSE_PATH}",
                "health": f"http://{HTTP_HOST}:{HTTP_PORT}/health"
            },
            "features": [
                "List files and folders",
                "Create files and folders",
                "Update file content and metadata",
                "Delete files and folders",
                "Copy files",
                "Search files with advanced queries",
                "File upload and download",
                "Permissions management"
            ]
        }

        await ctx.info("✅ Server healthy")
        return json.dumps(health_status, ensure_ascii=False, indent=2)

    except Exception as e:
        await ctx.error(f"❌ Health check failed: {str(e)}")
        return json.dumps({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False, indent=2)


def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(
                f"🚀 Khởi động Google Drive MCP Server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path=HTTP_PATH
            )

        elif transport == "sse":
            print(f"🚀 Khởi động Google Drive MCP Server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động Google Drive MCP Server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise


def main():
    """Hàm main để chạy server với transport fallback"""
    print("🚀 Starting Google Drive MCP Server")
    print("=" * 50)

    # Sử dụng transport mặc định của FastMCP
    transport_type = TRANSPORT_TYPE  # Copy global variable to local

    if transport_type == "sse":
        print(f"📡 Transport: SSE")
        print(f"🌐 SSE endpoint: http://{SSE_HOST}:{SSE_PORT}{SSE_PATH}")
        print(f"🏥 Health check: http://{SSE_HOST}:{SSE_PORT}/health")
        print("⚠️  Note: SSE transport fallback - Streamable HTTP transport preferred")

        # Sử dụng SSE transport
        try:
            mcp.run(transport="sse", host=SSE_HOST, port=SSE_PORT, path=SSE_PATH)
        except Exception as e:
            print(f"❌ SSE transport failed: {e}")
            print("🔄 Falling back to Streamable HTTP transport...")
            transport_type = "http"

    if transport_type == "http":
        print(f"📡 Transport: Streamable HTTP")
        print(f"🌐 HTTP endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print(f"🏥 Health check: http://{HTTP_HOST}:{HTTP_PORT}/health")
        print("🔧 Google Drive API: v3")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Google OAuth2 access token trong Authorization header")
        print("🔐 Required scopes:")
        for scope in SCOPES:
            print(f"   - {scope}")
        print()

        # Sử dụng Streamable HTTP transport (mặc định của FastMCP)
        try:
            mcp.run(transport="streamable-http", host=HTTP_HOST, port=HTTP_PORT, path=HTTP_PATH)
        except KeyboardInterrupt:
            print("\n⏹️  Server đã được dừng bởi người dùng")
        except Exception as e:
            print(f"❌ Lỗi khi khởi động server: {str(e)}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
