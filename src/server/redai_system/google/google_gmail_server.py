#!/usr/bin/env python3
"""
Google Gmail MCP Server sử dụng FastMCP và Gmail API v1

Server n<PERSON><PERSON> cung cấp các MCP tools để tương tác với Gmail API,
bao gồ<PERSON> gử<PERSON>, đ<PERSON><PERSON>, quả<PERSON> lý emails, drafts, labels và threads.

Tính năng:
- <PERSON><PERSON><PERSON> và nhận emails
- Quản lý drafts (t<PERSON><PERSON>, s<PERSON><PERSON>, g<PERSON><PERSON>)
- Quản lý labels và filters
- Tì<PERSON> kiếm emails
- Quản lý threads
- Đ<PERSON><PERSON> và download attachments

Transport: Streamable HTTP
Authentication: Google OAuth2 với access token từ client headers
"""

import os
import json
import asyncio
import base64
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Optional, Any, Union
from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request
from google.oauth2 import credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from datetime import datetime

# Cấu hình server
HTTP_HOST = os.getenv("GOOGLE_GMAIL_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_GMAIL_HTTP_PORT", "8018"))
HTTP_PATH = os.getenv("GOOGLE_GMAIL_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("GOOGLE_GMAIL_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GOOGLE_GMAIL_SSE_PORT", "8118"))
SSE_PATH = os.getenv("GOOGLE_GMAIL_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("GOOGLE_GMAIL_TRANSPORT", "http").lower()  # http hoặc sse

# Cấu hình Gmail API
SCOPES = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.modify',
    'https://www.googleapis.com/auth/gmail.compose'
]

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

class GmailClient:
    """Client để tương tác với Gmail API"""

    def __init__(self):
        # Không khởi tạo service cố định, sẽ tạo mới mỗi lần với token từ request
        pass

    def _create_service_with_token(self, access_token: str):
        """Tạo Gmail service với access token từ client"""
        try:
            # Tạo credentials từ access token
            creds = credentials.AccessTokenCredentials(access_token)

            # Tạo service với credentials
            service = build('gmail', 'v1', credentials=creds)
            return service

        except Exception as e:
            raise Exception(f"Lỗi tạo Gmail service với token: {str(e)}")

    def _get_service(self):
        """Lấy service với token từ request headers"""
        token = extract_bearer_token_from_request()
        if not token:
            raise Exception("Không tìm thấy Bearer token trong request headers. Vui lòng cung cấp Google OAuth2 access token.")

        return self._create_service_with_token(token)

    def _create_message(self, to: str, subject: str, body: str,
                       from_email: str = None, cc: str = None, bcc: str = None) -> Dict:
        """Tạo message object cho Gmail API"""
        message = MIMEText(body)
        message['to'] = to
        message['subject'] = subject

        if from_email:
            message['from'] = from_email
        if cc:
            message['cc'] = cc
        if bcc:
            message['bcc'] = bcc

        # Encode message
        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
        return {'raw': raw_message}

    async def send_message(self, to: str, subject: str, body: str,
                          from_email: str = None, cc: str = None, bcc: str = None) -> Dict:
        """Gửi email"""
        service = self._get_service()

        try:
            message = self._create_message(to, subject, body, from_email, cc, bcc)
            result = service.users().messages().send(userId='me', body=message).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi gửi email: {str(e)}")

    async def list_messages(self, query: str = None, max_results: int = 10,
                           label_ids: List[str] = None) -> Dict:
        """List messages trong Gmail"""
        service = self._get_service()

        try:
            params = {
                'userId': 'me',
                'maxResults': max_results
            }

            if query:
                params['q'] = query
            if label_ids:
                params['labelIds'] = label_ids

            result = service.users().messages().list(**params).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi list messages: {str(e)}")

    async def get_message(self, message_id: str, format: str = 'full') -> Dict:
        """Lấy thông tin message"""
        service = self._get_service()

        try:
            result = service.users().messages().get(
                userId='me',
                id=message_id,
                format=format
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi get message: {str(e)}")

    async def create_draft(self, to: str, subject: str, body: str,
                          from_email: str = None, cc: str = None, bcc: str = None) -> Dict:
        """Tạo draft"""
        service = self._get_service()

        try:
            message = self._create_message(to, subject, body, from_email, cc, bcc)
            draft = {'message': message}
            result = service.users().drafts().create(userId='me', body=draft).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi tạo draft: {str(e)}")

    async def send_draft(self, draft_id: str) -> Dict:
        """Gửi draft"""
        service = self._get_service()

        try:
            result = service.users().drafts().send(
                userId='me',
                body={'id': draft_id}
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi gửi draft: {str(e)}")

    async def list_labels(self) -> Dict:
        """List labels"""
        service = self._get_service()

        try:
            result = service.users().labels().list(userId='me').execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi list labels: {str(e)}")

    async def create_label(self, name: str, label_list_visibility: str = 'labelShow',
                          message_list_visibility: str = 'show') -> Dict:
        """Tạo label mới"""
        service = self._get_service()

        try:
            label = {
                'name': name,
                'labelListVisibility': label_list_visibility,
                'messageListVisibility': message_list_visibility
            }
            result = service.users().labels().create(userId='me', body=label).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi tạo label: {str(e)}")

    async def modify_message(self, message_id: str, add_label_ids: List[str] = None,
                           remove_label_ids: List[str] = None) -> Dict:
        """Modify message labels"""
        service = self._get_service()

        try:
            body = {}
            if add_label_ids:
                body['addLabelIds'] = add_label_ids
            if remove_label_ids:
                body['removeLabelIds'] = remove_label_ids

            result = service.users().messages().modify(
                userId='me',
                id=message_id,
                body=body
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi modify message: {str(e)}")

# Khởi tạo Gmail client
gmail_client = GmailClient()

# Khởi tạo MCP server
mcp = FastMCP("Google-Gmail-Server")

@mcp.tool(description="Gửi email qua Gmail")
async def send_email_tool(
    ctx,
    to: str,
    subject: str,
    body: str,
    from_email: str = "",
    cc: str = "",
    bcc: str = ""
) -> str:
    """
    Gửi email qua Gmail

    Args:
        to: Địa chỉ email người nhận
        subject: Tiêu đề email
        body: Nội dung email
        from_email: Địa chỉ email người gửi (tùy chọn)
        cc: Địa chỉ CC (tùy chọn)
        bcc: Địa chỉ BCC (tùy chọn)

    Returns:
        JSON string chứa thông tin email đã gửi
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📧 Gửi email đến: {to}")
        await ctx.info(f"📝 Subject: {subject}")
        await ctx.info(f"📄 Body length: {len(body)} characters")

        if cc:
            await ctx.info(f"📋 CC: {cc}")
        if bcc:
            await ctx.info(f"🔒 BCC: {bcc}")

        result = await gmail_client.send_message(
            to=to,
            subject=subject,
            body=body,
            from_email=from_email if from_email else None,
            cc=cc if cc else None,
            bcc=bcc if bcc else None
        )

        message_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Email đã gửi thành công - Message ID: {message_id}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi gửi email: {str(e)}")
        return f"❌ Lỗi gửi email: {str(e)}"

@mcp.tool(description="List emails trong Gmail")
async def list_emails_tool(
    ctx,
    query: str = "",
    max_results: int = 10,
    label_ids: str = ""
) -> str:
    """
    List emails trong Gmail

    Args:
        query: Query string để tìm kiếm emails (ví dụ: "from:<EMAIL>")
        max_results: Số lượng emails trả về (mặc định 10)
        label_ids: Danh sách label IDs cách nhau bởi dấu phẩy (tùy chọn)

    Returns:
        JSON string chứa danh sách emails
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📧 List emails với query: '{query}', max_results: {max_results}")

        label_list = [l.strip() for l in label_ids.split(",") if l.strip()] if label_ids else None
        if label_list:
            await ctx.info(f"🏷️ Label IDs: {label_list}")

        result = await gmail_client.list_messages(
            query=query if query else None,
            max_results=max_results,
            label_ids=label_list
        )

        messages = result.get('messages', [])
        await ctx.info(f"✅ Tìm thấy {len(messages)} emails")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi list emails: {str(e)}")
        return f"❌ Lỗi list emails: {str(e)}"

@mcp.tool(description="Lấy thông tin chi tiết về email")
async def get_email_tool(
    ctx,
    message_id: str,
    format: str = "full"
) -> str:
    """
    Lấy thông tin chi tiết về email

    Args:
        message_id: ID của email
        format: Format trả về ("full", "metadata", "minimal", "raw")

    Returns:
        JSON string chứa thông tin email
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📧 Lấy thông tin email: {message_id}, format: {format}")

        result = await gmail_client.get_message(message_id, format)

        # Log thông tin cơ bản
        snippet = result.get('snippet', 'No snippet')
        await ctx.info(f"✅ Email snippet: {snippet[:100]}...")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi get email: {str(e)}")
        return f"❌ Lỗi get email: {str(e)}"

@mcp.tool(description="Tạo draft email")
async def create_draft_tool(
    ctx,
    to: str,
    subject: str,
    body: str,
    from_email: str = "",
    cc: str = "",
    bcc: str = ""
) -> str:
    """
    Tạo draft email

    Args:
        to: Địa chỉ email người nhận
        subject: Tiêu đề email
        body: Nội dung email
        from_email: Địa chỉ email người gửi (tùy chọn)
        cc: Địa chỉ CC (tùy chọn)
        bcc: Địa chỉ BCC (tùy chọn)

    Returns:
        JSON string chứa thông tin draft đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📝 Tạo draft email đến: {to}")
        await ctx.info(f"📝 Subject: {subject}")
        await ctx.info(f"📄 Body length: {len(body)} characters")

        result = await gmail_client.create_draft(
            to=to,
            subject=subject,
            body=body,
            from_email=from_email if from_email else None,
            cc=cc if cc else None,
            bcc=bcc if bcc else None
        )

        draft_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Draft đã tạo thành công - Draft ID: {draft_id}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo draft: {str(e)}")
        return f"❌ Lỗi tạo draft: {str(e)}"

@mcp.tool(description="Gửi draft email")
async def send_draft_tool(
    ctx,
    draft_id: str
) -> str:
    """
    Gửi draft email

    Args:
        draft_id: ID của draft cần gửi

    Returns:
        JSON string chứa thông tin email đã gửi
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📧 Gửi draft: {draft_id}")

        result = await gmail_client.send_draft(draft_id)

        message_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Draft đã gửi thành công - Message ID: {message_id}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi gửi draft: {str(e)}")
        return f"❌ Lỗi gửi draft: {str(e)}"

@mcp.tool(description="List tất cả labels trong Gmail")
async def list_labels_tool(
    ctx
) -> str:
    """
    List tất cả labels trong Gmail

    Returns:
        JSON string chứa danh sách labels
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🏷️ List labels")

        result = await gmail_client.list_labels()

        labels = result.get('labels', [])
        await ctx.info(f"✅ Tìm thấy {len(labels)} labels")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi list labels: {str(e)}")
        return f"❌ Lỗi list labels: {str(e)}"

@mcp.tool(description="Tạo label mới trong Gmail")
async def create_label_tool(
    ctx,
    name: str,
    label_list_visibility: str = "labelShow",
    message_list_visibility: str = "show"
) -> str:
    """
    Tạo label mới trong Gmail

    Args:
        name: Tên của label
        label_list_visibility: Hiển thị trong danh sách labels ("labelShow", "labelHide")
        message_list_visibility: Hiển thị trong danh sách messages ("show", "hide")

    Returns:
        JSON string chứa thông tin label đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🏷️ Tạo label: {name}")
        await ctx.info(f"👁️ Visibility: list={label_list_visibility}, message={message_list_visibility}")

        result = await gmail_client.create_label(
            name=name,
            label_list_visibility=label_list_visibility,
            message_list_visibility=message_list_visibility
        )

        label_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Label đã tạo thành công - Label ID: {label_id}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo label: {str(e)}")
        return f"❌ Lỗi tạo label: {str(e)}"

@mcp.tool(description="Thay đổi labels của email")
async def modify_email_labels_tool(
    ctx,
    message_id: str,
    add_label_ids: str = "",
    remove_label_ids: str = ""
) -> str:
    """
    Thay đổi labels của email

    Args:
        message_id: ID của email
        add_label_ids: Danh sách label IDs cần thêm, cách nhau bởi dấu phẩy
        remove_label_ids: Danh sách label IDs cần xóa, cách nhau bởi dấu phẩy

    Returns:
        JSON string chứa kết quả modify
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🏷️ Modify labels cho email: {message_id}")

        add_list = [l.strip() for l in add_label_ids.split(",") if l.strip()] if add_label_ids else None
        remove_list = [l.strip() for l in remove_label_ids.split(",") if l.strip()] if remove_label_ids else None

        if add_list:
            await ctx.info(f"➕ Add labels: {add_list}")
        if remove_list:
            await ctx.info(f"➖ Remove labels: {remove_list}")

        result = await gmail_client.modify_message(
            message_id=message_id,
            add_label_ids=add_list,
            remove_label_ids=remove_list
        )

        await ctx.info(f"✅ Labels đã được modify thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi modify labels: {str(e)}")
        return f"❌ Lỗi modify labels: {str(e)}"

@mcp.tool(description="Tìm kiếm emails nâng cao trong Gmail")
async def search_emails_tool(
    ctx,
    search_term: str,
    from_email: str = "",
    to_email: str = "",
    subject: str = "",
    has_attachment: bool = False,
    is_unread: bool = False,
    max_results: int = 20
) -> str:
    """
    Tìm kiếm emails nâng cao trong Gmail

    Args:
        search_term: Từ khóa tìm kiếm
        from_email: Email người gửi (tùy chọn)
        to_email: Email người nhận (tùy chọn)
        subject: Từ khóa trong subject (tùy chọn)
        has_attachment: Chỉ tìm emails có attachment (tùy chọn)
        is_unread: Chỉ tìm emails chưa đọc (tùy chọn)
        max_results: Số lượng kết quả trả về (mặc định 20)

    Returns:
        JSON string chứa kết quả tìm kiếm
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        # Xây dựng query
        query_parts = []

        if search_term:
            query_parts.append(search_term)

        if from_email:
            query_parts.append(f"from:{from_email}")

        if to_email:
            query_parts.append(f"to:{to_email}")

        if subject:
            query_parts.append(f"subject:{subject}")

        if has_attachment:
            query_parts.append("has:attachment")

        if is_unread:
            query_parts.append("is:unread")

        query = " ".join(query_parts)

        await ctx.info(f"🔍 Tìm kiếm với query: {query}")

        result = await gmail_client.list_messages(
            query=query,
            max_results=max_results
        )

        messages = result.get('messages', [])
        await ctx.info(f"✅ Tìm thấy {len(messages)} emails")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi search emails: {str(e)}")
        return f"❌ Lỗi search emails: {str(e)}"

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động Gmail MCP Server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path=HTTP_PATH
            )

        elif transport == "sse":
            print(f"🚀 Khởi động Gmail MCP Server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động Gmail MCP Server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def main():
    """Hàm main để chạy server"""
    try:
        print("=" * 70)
        print("📧 Khởi động Gmail MCP Server")
        print("=" * 70)
        print("📋 Transport: Streamable HTTP (FastMCP)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print("🔧 Gmail API: v1")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Google OAuth2 access token trong Authorization header")
        print("🔐 Required scopes:")
        for scope in SCOPES:
            print(f"   - {scope}")
        print()

        print("=" * 70)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()