#!/usr/bin/env python3
"""
Google Calendar MCP Server sử dụng FastMCP và Google Calendar API v3

Server này cung cấp các MCP tools để tương tác với Google Calendar API,
bao gồm quản lý events, calendars, và freebusy information.

Tính năng:
- Quản lý events (tạo, sửa, xóa, list)
- Quản lý calendars (tạo, sửa, xóa, list)
- Quick add events từ text
- Freebusy queries
- Calendar sharing và permissions
- Recurring events support

Transport: Streamable HTTP
Authentication: Google OAuth2 với access token từ client headers
"""

import os
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request
from google.oauth2 import credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Google Calendar API scopes
SCOPES = [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
    'https://www.googleapis.com/auth/calendar.readonly'
]

# Cấu hình server
HTTP_HOST = os.getenv("GOOGLE_CALENDAR_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_CALENDAR_HTTP_PORT", "8019"))
HTTP_PATH = os.getenv("GOOGLE_CALENDAR_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("GOOGLE_CALENDAR_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GOOGLE_CALENDAR_SSE_PORT", "8119"))
SSE_PATH = os.getenv("GOOGLE_CALENDAR_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("GOOGLE_CALENDAR_TRANSPORT", "http").lower()  # http hoặc sse

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

class CalendarClient:
    """Client để tương tác với Google Calendar API"""

    def __init__(self):
        # Không khởi tạo service cố định, sẽ tạo mới mỗi lần với token từ request
        pass

    def _create_service_with_token(self, access_token: str):
        """Tạo Calendar service với access token từ client"""
        try:
            # Tạo credentials từ access token
            creds = credentials.AccessTokenCredentials(access_token)

            # Tạo service với credentials
            service = build('calendar', 'v3', credentials=creds)
            return service

        except Exception as e:
            raise Exception(f"Lỗi tạo Calendar service với token: {str(e)}")

    def _get_service(self):
        """Lấy service với token từ request headers"""
        token = extract_bearer_token_from_request()
        if not token:
            raise Exception("Không tìm thấy Bearer token trong request headers. Vui lòng cung cấp Google OAuth2 access token.")

        return self._create_service_with_token(token)

    async def list_calendars(self) -> Dict:
        """List calendars của user"""
        service = self._get_service()

        try:
            result = service.calendarList().list().execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi list calendars: {str(e)}")

    async def get_calendar(self, calendar_id: str) -> Dict:
        """Lấy thông tin calendar"""
        service = self._get_service()

        try:
            result = service.calendars().get(calendarId=calendar_id).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi get calendar: {str(e)}")

    async def create_calendar(self, summary: str, description: str = None,
                            time_zone: str = None) -> Dict:
        """Tạo calendar mới"""
        service = self._get_service()

        try:
            calendar = {
                'summary': summary
            }

            if description:
                calendar['description'] = description
            if time_zone:
                calendar['timeZone'] = time_zone

            result = service.calendars().insert(body=calendar).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi tạo calendar: {str(e)}")

    async def update_calendar(self, calendar_id: str, summary: str = None,
                            description: str = None, time_zone: str = None) -> Dict:
        """Update calendar"""
        service = self._get_service()

        try:
            # Lấy calendar hiện tại
            calendar = service.calendars().get(calendarId=calendar_id).execute()

            # Update các fields
            if summary:
                calendar['summary'] = summary
            if description:
                calendar['description'] = description
            if time_zone:
                calendar['timeZone'] = time_zone

            result = service.calendars().update(calendarId=calendar_id, body=calendar).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi update calendar: {str(e)}")

    async def delete_calendar(self, calendar_id: str) -> Dict:
        """Xóa calendar"""
        service = self._get_service()

        try:
            service.calendars().delete(calendarId=calendar_id).execute()
            return {"success": True, "message": f"Calendar {calendar_id} đã được xóa"}
        except HttpError as e:
            raise Exception(f"Lỗi xóa calendar: {str(e)}")

    async def list_events(self, calendar_id: str = 'primary', time_min: str = None,
                         time_max: str = None, max_results: int = 10,
                         single_events: bool = True, order_by: str = 'startTime') -> Dict:
        """List events trong calendar"""
        service = self._get_service()

        try:
            params = {
                'calendarId': calendar_id,
                'maxResults': max_results,
                'singleEvents': single_events,
                'orderBy': order_by
            }

            if time_min:
                params['timeMin'] = time_min
            if time_max:
                params['timeMax'] = time_max

            result = service.events().list(**params).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi list events: {str(e)}")

    async def get_event(self, calendar_id: str, event_id: str) -> Dict:
        """Lấy thông tin event"""
        service = self._get_service()

        try:
            result = service.events().get(calendarId=calendar_id, eventId=event_id).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi get event: {str(e)}")

    async def create_event(self, calendar_id: str, summary: str, start_time: str,
                          end_time: str, description: str = None, location: str = None,
                          attendees: List[str] = None, time_zone: str = None) -> Dict:
        """Tạo event mới"""
        service = self._get_service()

        try:
            event = {
                'summary': summary,
                'start': {
                    'dateTime': start_time,
                    'timeZone': time_zone or 'UTC'
                },
                'end': {
                    'dateTime': end_time,
                    'timeZone': time_zone or 'UTC'
                }
            }

            if description:
                event['description'] = description
            if location:
                event['location'] = location
            if attendees:
                event['attendees'] = [{'email': email} for email in attendees]

            result = service.events().insert(calendarId=calendar_id, body=event).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi tạo event: {str(e)}")

    async def update_event(self, calendar_id: str, event_id: str, summary: str = None,
                          start_time: str = None, end_time: str = None,
                          description: str = None, location: str = None) -> Dict:
        """Update event"""
        service = self._get_service()

        try:
            # Lấy event hiện tại
            event = service.events().get(calendarId=calendar_id, eventId=event_id).execute()

            # Update các fields
            if summary:
                event['summary'] = summary
            if description:
                event['description'] = description
            if location:
                event['location'] = location
            if start_time:
                event['start']['dateTime'] = start_time
            if end_time:
                event['end']['dateTime'] = end_time

            result = service.events().update(calendarId=calendar_id, eventId=event_id, body=event).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi update event: {str(e)}")

    async def delete_event(self, calendar_id: str, event_id: str) -> Dict:
        """Xóa event"""
        service = self._get_service()

        try:
            service.events().delete(calendarId=calendar_id, eventId=event_id).execute()
            return {"success": True, "message": f"Event {event_id} đã được xóa"}
        except HttpError as e:
            raise Exception(f"Lỗi xóa event: {str(e)}")

    async def quick_add_event(self, calendar_id: str, text: str) -> Dict:
        """Tạo event nhanh từ text"""
        service = self._get_service()

        try:
            result = service.events().quickAdd(calendarId=calendar_id, text=text).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi quick add event: {str(e)}")

    async def freebusy_query(self, calendars: List[str], time_min: str, time_max: str) -> Dict:
        """Query freebusy information"""
        service = self._get_service()

        try:
            body = {
                'timeMin': time_min,
                'timeMax': time_max,
                'items': [{'id': cal_id} for cal_id in calendars]
            }

            result = service.freebusy().query(body=body).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi freebusy query: {str(e)}")

# Khởi tạo Calendar client
calendar_client = CalendarClient()

# Khởi tạo MCP server
mcp = FastMCP("Google-Calendar-Server")

# FastMCP tự động handle tools/list từ các @mcp.tool() decorators
# Không cần implement list_tools manually

# @mcp.tool()
# async def list_tools() -> str:
#     """
#     List tất cả tools có sẵn trong Google Calendar MCP Server

#     Returns:
#         JSON string chứa danh sách tools và mô tả
#     """
#     tools = [
#         {
#             "name": "list_calendars_tool",
#             "description": "List tất cả calendars của user",
#             "category": "calendars",
#             "parameters": {}
#         },
#         {
#             "name": "get_calendar_tool",
#             "description": "Lấy thông tin chi tiết về calendar",
#             "category": "calendars",
#             "parameters": {
#                 "calendar_id": "ID của calendar (required)"
#             }
#         },
#         {
#             "name": "create_calendar_tool",
#             "description": "Tạo calendar mới",
#             "category": "calendars",
#             "parameters": {
#                 "summary": "Tên của calendar (required)",
#                 "description": "Mô tả calendar (optional)",
#                 "time_zone": "Time zone của calendar (optional, ví dụ: Asia/Ho_Chi_Minh)"
#             }
#         },
#         {
#             "name": "update_calendar_tool",
#             "description": "Update calendar",
#             "category": "calendars",
#             "parameters": {
#                 "calendar_id": "ID của calendar cần update (required)",
#                 "summary": "Tên mới của calendar (optional)",
#                 "description": "Mô tả mới (optional)",
#                 "time_zone": "Time zone mới (optional)"
#             }
#         },
#         {
#             "name": "delete_calendar_tool",
#             "description": "Xóa calendar",
#             "category": "calendars",
#             "parameters": {
#                 "calendar_id": "ID của calendar cần xóa (required)"
#             }
#         },
#         {
#             "name": "list_events_tool",
#             "description": "List events trong calendar",
#             "category": "events",
#             "parameters": {
#                 "calendar_id": "ID của calendar (default: primary)",
#                 "time_min": "Thời gian bắt đầu (ISO format, optional)",
#                 "time_max": "Thời gian kết thúc (ISO format, optional)",
#                 "max_results": "Số lượng events trả về (default: 10)"
#             }
#         },
#         {
#             "name": "get_event_tool",
#             "description": "Lấy thông tin chi tiết về event",
#             "category": "events",
#             "parameters": {
#                 "calendar_id": "ID của calendar (required)",
#                 "event_id": "ID của event (required)"
#             }
#         },
#         {
#             "name": "create_event_tool",
#             "description": "Tạo event mới",
#             "category": "events",
#             "parameters": {
#                 "calendar_id": "ID của calendar (required)",
#                 "summary": "Tiêu đề event (required)",
#                 "start_time": "Thời gian bắt đầu (ISO format, required)",
#                 "end_time": "Thời gian kết thúc (ISO format, required)",
#                 "description": "Mô tả event (optional)",
#                 "location": "Địa điểm (optional)",
#                 "attendees": "Danh sách email người tham gia, cách nhau bởi dấu phẩy (optional)",
#                 "time_zone": "Time zone (default: UTC)"
#             }
#         },
#         {
#             "name": "update_event_tool",
#             "description": "Update event",
#             "category": "events",
#             "parameters": {
#                 "calendar_id": "ID của calendar (required)",
#                 "event_id": "ID của event cần update (required)",
#                 "summary": "Tiêu đề mới (optional)",
#                 "start_time": "Thời gian bắt đầu mới (ISO format, optional)",
#                 "end_time": "Thời gian kết thúc mới (ISO format, optional)",
#                 "description": "Mô tả mới (optional)",
#                 "location": "Địa điểm mới (optional)"
#             }
#         },
#         {
#             "name": "delete_event_tool",
#             "description": "Xóa event",
#             "category": "events",
#             "parameters": {
#                 "calendar_id": "ID của calendar (required)",
#                 "event_id": "ID của event cần xóa (required)"
#             }
#         },
#         {
#             "name": "quick_add_event_tool",
#             "description": "Tạo event nhanh từ text tự nhiên",
#             "category": "events",
#             "parameters": {
#                 "calendar_id": "ID của calendar (required)",
#                 "text": "Text mô tả event (required, ví dụ: Meeting tomorrow 2pm-3pm)"
#             }
#         },
#         {
#             "name": "freebusy_query_tool",
#             "description": "Query freebusy information cho calendars",
#             "category": "freebusy",
#             "parameters": {
#                 "calendars": "Danh sách calendar IDs cách nhau bởi dấu phẩy (required)",
#                 "time_min": "Thời gian bắt đầu (ISO format, required)",
#                 "time_max": "Thời gian kết thúc (ISO format, required)"
#             }
#         }
#     ]

#     return json.dumps({
#         "server": "Google Calendar MCP Server",
#         "version": "1.0.0",
#         "api": "Google Calendar API v3",
#         "total_tools": len(tools),
#         "tools": tools
#     }, ensure_ascii=False, indent=2)

@mcp.tool(description="List tất cả calendars của user")
async def list_calendars_tool(
    ctx
) -> str:
    """
    List tất cả calendars của user

    Returns:
        JSON string chứa danh sách calendars
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 List calendars")

        result = await calendar_client.list_calendars()

        calendars = result.get('items', [])
        await ctx.info(f"✅ Tìm thấy {len(calendars)} calendars")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi list calendars: {str(e)}")
        return f"❌ Lỗi list calendars: {str(e)}"

@mcp.tool()
async def get_calendar_tool(
    ctx,
    calendar_id: str
) -> str:
    """
    Lấy thông tin chi tiết về calendar

    Args:
        calendar_id: ID của calendar

    Returns:
        JSON string chứa thông tin calendar
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 Get calendar: {calendar_id}")

        result = await calendar_client.get_calendar(calendar_id)

        calendar_name = result.get('summary', 'Unknown')
        await ctx.info(f"✅ Calendar: {calendar_name}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi get calendar: {str(e)}")
        return f"❌ Lỗi get calendar: {str(e)}"

@mcp.tool()
async def create_calendar_tool(
    ctx,
    summary: str,
    description: str = "",
    time_zone: str = ""
) -> str:
    """
    Tạo calendar mới

    Args:
        summary: Tên của calendar
        description: Mô tả calendar (tùy chọn)
        time_zone: Time zone của calendar (tùy chọn, ví dụ: "Asia/Ho_Chi_Minh")

    Returns:
        JSON string chứa thông tin calendar đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 Tạo calendar: {summary}")
        if description:
            await ctx.info(f"📝 Description: {description}")
        if time_zone:
            await ctx.info(f"🌍 Time zone: {time_zone}")

        result = await calendar_client.create_calendar(
            summary=summary,
            description=description if description else None,
            time_zone=time_zone if time_zone else None
        )

        calendar_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Calendar đã tạo thành công - ID: {calendar_id}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo calendar: {str(e)}")
        return f"❌ Lỗi tạo calendar: {str(e)}"

@mcp.tool()
async def update_calendar_tool(
    ctx,
    calendar_id: str,
    summary: str = "",
    description: str = "",
    time_zone: str = ""
) -> str:
    """
    Update calendar

    Args:
        calendar_id: ID của calendar cần update
        summary: Tên mới của calendar (tùy chọn)
        description: Mô tả mới (tùy chọn)
        time_zone: Time zone mới (tùy chọn)

    Returns:
        JSON string chứa thông tin calendar đã update
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 Update calendar: {calendar_id}")

        result = await calendar_client.update_calendar(
            calendar_id=calendar_id,
            summary=summary if summary else None,
            description=description if description else None,
            time_zone=time_zone if time_zone else None
        )

        await ctx.info(f"✅ Calendar đã update thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi update calendar: {str(e)}")
        return f"❌ Lỗi update calendar: {str(e)}"

@mcp.tool()
async def delete_calendar_tool(
    ctx,
    calendar_id: str
) -> str:
    """
    Xóa calendar

    Args:
        calendar_id: ID của calendar cần xóa

    Returns:
        JSON string chứa kết quả xóa
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🗑️ Xóa calendar: {calendar_id}")

        result = await calendar_client.delete_calendar(calendar_id)

        await ctx.info(f"✅ Calendar đã xóa thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi xóa calendar: {str(e)}")
        return f"❌ Lỗi xóa calendar: {str(e)}"

@mcp.tool()
async def list_events_tool(
    ctx,
    calendar_id: str = "primary",
    time_min: str = "",
    time_max: str = "",
    max_results: int = 10
) -> str:
    """
    List events trong calendar

    Args:
        calendar_id: ID của calendar (mặc định "primary")
        time_min: Thời gian bắt đầu (ISO format, tùy chọn)
        time_max: Thời gian kết thúc (ISO format, tùy chọn)
        max_results: Số lượng events trả về (mặc định 10)

    Returns:
        JSON string chứa danh sách events
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 List events trong calendar: {calendar_id}")
        await ctx.info(f"📊 Max results: {max_results}")

        if time_min:
            await ctx.info(f"⏰ Time min: {time_min}")
        if time_max:
            await ctx.info(f"⏰ Time max: {time_max}")

        result = await calendar_client.list_events(
            calendar_id=calendar_id,
            time_min=time_min if time_min else None,
            time_max=time_max if time_max else None,
            max_results=max_results
        )

        events = result.get('items', [])
        await ctx.info(f"✅ Tìm thấy {len(events)} events")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi list events: {str(e)}")
        return f"❌ Lỗi list events: {str(e)}"

@mcp.tool()
async def get_event_tool(
    ctx,
    calendar_id: str,
    event_id: str
) -> str:
    """
    Lấy thông tin chi tiết về event

    Args:
        calendar_id: ID của calendar
        event_id: ID của event

    Returns:
        JSON string chứa thông tin event
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 Get event: {event_id} trong calendar: {calendar_id}")

        result = await calendar_client.get_event(calendar_id, event_id)

        event_summary = result.get('summary', 'Unknown')
        await ctx.info(f"✅ Event: {event_summary}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi get event: {str(e)}")
        return f"❌ Lỗi get event: {str(e)}"

@mcp.tool(description="Tạo event mới")
async def create_event_tool(
    ctx,
    calendar_id: str,
    summary: str,
    start_time: str,
    end_time: str,
    description: str = "",
    location: str = "",
    attendees: str = "",
    time_zone: str = "UTC"
) -> str:
    """
    Tạo event mới

    Args:
        calendar_id: ID của calendar
        summary: Tiêu đề event
        start_time: Thời gian bắt đầu (ISO format, ví dụ: "2024-01-01T10:00:00")
        end_time: Thời gian kết thúc (ISO format, ví dụ: "2024-01-01T11:00:00")
        description: Mô tả event (tùy chọn)
        location: Địa điểm (tùy chọn)
        attendees: Danh sách email người tham gia, cách nhau bởi dấu phẩy (tùy chọn)
        time_zone: Time zone (mặc định UTC)

    Returns:
        JSON string chứa thông tin event đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 Tạo event: {summary}")
        await ctx.info(f"⏰ Start: {start_time}")
        await ctx.info(f"⏰ End: {end_time}")
        await ctx.info(f"🌍 Time zone: {time_zone}")

        if description:
            await ctx.info(f"📝 Description: {description}")
        if location:
            await ctx.info(f"📍 Location: {location}")

        attendees_list = [email.strip() for email in attendees.split(",") if email.strip()] if attendees else None
        if attendees_list:
            await ctx.info(f"👥 Attendees: {attendees_list}")

        result = await calendar_client.create_event(
            calendar_id=calendar_id,
            summary=summary,
            start_time=start_time,
            end_time=end_time,
            description=description if description else None,
            location=location if location else None,
            attendees=attendees_list,
            time_zone=time_zone
        )

        event_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Event đã tạo thành công - ID: {event_id}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo event: {str(e)}")
        return f"❌ Lỗi tạo event: {str(e)}"

@mcp.tool()
async def update_event_tool(
    ctx,
    calendar_id: str,
    event_id: str,
    summary: str = "",
    start_time: str = "",
    end_time: str = "",
    description: str = "",
    location: str = ""
) -> str:
    """
    Update event

    Args:
        calendar_id: ID của calendar
        event_id: ID của event cần update
        summary: Tiêu đề mới (tùy chọn)
        start_time: Thời gian bắt đầu mới (ISO format, tùy chọn)
        end_time: Thời gian kết thúc mới (ISO format, tùy chọn)
        description: Mô tả mới (tùy chọn)
        location: Địa điểm mới (tùy chọn)

    Returns:
        JSON string chứa thông tin event đã update
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📅 Update event: {event_id}")

        result = await calendar_client.update_event(
            calendar_id=calendar_id,
            event_id=event_id,
            summary=summary if summary else None,
            start_time=start_time if start_time else None,
            end_time=end_time if end_time else None,
            description=description if description else None,
            location=location if location else None
        )

        await ctx.info(f"✅ Event đã update thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi update event: {str(e)}")
        return f"❌ Lỗi update event: {str(e)}"

@mcp.tool()
async def delete_event_tool(
    ctx,
    calendar_id: str,
    event_id: str
) -> str:
    """
    Xóa event

    Args:
        calendar_id: ID của calendar
        event_id: ID của event cần xóa

    Returns:
        JSON string chứa kết quả xóa
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🗑️ Xóa event: {event_id}")

        result = await calendar_client.delete_event(calendar_id, event_id)

        await ctx.info(f"✅ Event đã xóa thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi xóa event: {str(e)}")
        return f"❌ Lỗi xóa event: {str(e)}"

@mcp.tool()
async def quick_add_event_tool(
    ctx,
    calendar_id: str,
    text: str
) -> str:
    """
    Tạo event nhanh từ text tự nhiên

    Args:
        calendar_id: ID của calendar
        text: Text mô tả event (ví dụ: "Meeting tomorrow 2pm-3pm")

    Returns:
        JSON string chứa thông tin event đã tạo
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"⚡ Quick add event: {text}")

        result = await calendar_client.quick_add_event(calendar_id, text)

        event_summary = result.get('summary', 'Unknown')
        event_id = result.get('id', 'Unknown')
        await ctx.info(f"✅ Event đã tạo thành công: {event_summary} (ID: {event_id})")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi quick add event: {str(e)}")
        return f"❌ Lỗi quick add event: {str(e)}"

@mcp.tool()
async def freebusy_query_tool(
    ctx,
    calendars: str,
    time_min: str,
    time_max: str
) -> str:
    """
    Query freebusy information cho calendars

    Args:
        calendars: Danh sách calendar IDs cách nhau bởi dấu phẩy
        time_min: Thời gian bắt đầu (ISO format)
        time_max: Thời gian kết thúc (ISO format)

    Returns:
        JSON string chứa thông tin freebusy
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        calendar_list = [cal.strip() for cal in calendars.split(",") if cal.strip()]

        await ctx.info(f"🔍 Freebusy query cho {len(calendar_list)} calendars")
        await ctx.info(f"⏰ Time range: {time_min} - {time_max}")
        await ctx.info(f"📅 Calendars: {calendar_list}")

        result = await calendar_client.freebusy_query(calendar_list, time_min, time_max)

        await ctx.info(f"✅ Freebusy query thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi freebusy query: {str(e)}")
        return f"❌ Lỗi freebusy query: {str(e)}"

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động Calendar MCP Server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path=HTTP_PATH
            )

        elif transport == "sse":
            print(f"🚀 Khởi động Calendar MCP Server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động Calendar MCP Server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def main():
    """Hàm main để chạy server"""
    try:
        print("=" * 70)
        print("📅 Khởi động Google Calendar MCP Server")
        print("=" * 70)
        print("📋 Transport: Streamable HTTP (FastMCP)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print("🔧 Calendar API: v3")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Google OAuth2 access token trong Authorization header")
        print("🔐 Required scopes:")
        for scope in SCOPES:
            print(f"   - {scope}")
        print()

        print("=" * 70)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()