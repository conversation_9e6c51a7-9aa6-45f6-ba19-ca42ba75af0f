# Google Analytics MCP Server

MCP Server cho Google Analytics API sử dụng **MCP Python SDK chính thức**.

## 🎯 Tính năng

### ✅ Đã được sửa theo chuẩn MCP Python SDK:
- **Sử dụng `mcp.server.fastmcp.FastMCP`** thay vì `fastmcp.FastMCP`
- **Xóa `@mcp._mcp_list_tools()`** - FastMCP tự động handle tools
- **Xóa manual `list_tools()`** - Không cần thiết với MCP SDK
- **Sử dụng `@mcp.tool()` decorators** - Cách chuẩn của MCP SDK
- **Streamable HTTP transport** - Transport mặc định của MCP SDK
- **SSE fallback** - Tự động fallback nếu HTTP fail
- **Thêm missing tools** - Implement đầy đủ pivot report tool

### 📊 Google Analytics 4 Features:
- **Standard Reports** - Chạy reports với dimensions/metrics tùy chỉnh
- **Realtime Reports** - Dữ liệu realtime từ GA4
- **Pivot Reports** - Reports với pivot tables
- **Batch Reports** - Chạy nhiều reports cùng lúc
- **Metadata** - Lấy danh sách dimensions/metrics có sẵn
- **Compatibility Check** - Kiểm tra tương thích dimensions/metrics
- **Popular Pages** - Trang phổ biến nhất
- **Traffic Sources** - Nguồn traffic chi tiết

## 🔧 Cài đặt

### Dependencies
```bash
# Cài đặt MCP SDK
pip install mcp>=1.0.0

# Cài đặt Google Analytics Data API
pip install google-analytics-data google-auth

# Hoặc cài đặt tất cả cùng lúc
pip install google-analytics-data google-auth mcp>=1.0.0
```

### ⚠️ Lưu ý về Dependencies
- Nếu chưa cài `google-analytics-data`, server vẫn chạy được nhưng tools sẽ trả về lỗi
- Server sẽ hiển thị warning và hướng dẫn cài đặt khi khởi động
- Cần Google OAuth2 credentials để sử dụng GA4 API

### Environment Variables
```env
# === SERVER CONFIGURATION ===
GOOGLE_ANALYTICS_HTTP_HOST=127.0.0.1
GOOGLE_ANALYTICS_HTTP_PORT=8021
GOOGLE_ANALYTICS_HTTP_PATH=/mcp

# === SSE FALLBACK CONFIGURATION ===
GOOGLE_ANALYTICS_SSE_HOST=127.0.0.1
GOOGLE_ANALYTICS_SSE_PORT=8122
GOOGLE_ANALYTICS_SSE_PATH=/sse

# === TRANSPORT TYPE ===
GOOGLE_ANALYTICS_TRANSPORT=http  # hoặc "sse"
```

## 🚀 Chạy Server

```bash
python src/server/redai_system/google/google_analytics_server.py
```

Server sẽ khởi động với:
- **HTTP Transport**: `http://127.0.0.1:8021/mcp`
- **SSE Fallback**: `http://127.0.0.1:8122/sse` (nếu HTTP fail)
- **Health Check**: `http://127.0.0.1:8021/health`

## 🛠️ Tools Available

### 1. **run_report_tool**
Chạy standard report từ GA4
```json
{
  "property_id": "123456789",
  "dimensions": "country,city",
  "metrics": "sessions,users",
  "start_date": "30daysAgo",
  "end_date": "today",
  "limit": 100
}
```

### 2. **get_realtime_report_tool**
Lấy realtime data từ GA4
```json
{
  "property_id": "123456789",
  "dimensions": "country",
  "metrics": "activeUsers",
  "limit": 50
}
```

### 3. **run_pivot_report_tool**
Chạy pivot report với pivot tables
```json
{
  "property_id": "123456789",
  "dimensions": "country,source",
  "metrics": "sessions,users",
  "pivots": "[{\"field_names\": [\"country\"], \"limit\": 10}]",
  "start_date": "7daysAgo",
  "end_date": "today"
}
```

### 4. **batch_run_reports_tool**
Chạy nhiều reports cùng lúc
```json
{
  "property_id": "123456789",
  "reports": "[{\"dimensions\": [\"country\"], \"metrics\": [\"sessions\"]}, {\"dimensions\": [\"source\"], \"metrics\": [\"users\"]}]"
}
```

### 5. **get_metadata_tool**
Lấy metadata về dimensions/metrics
```json
{
  "property_id": "123456789"
}
```

### 6. **check_compatibility_tool**
Kiểm tra compatibility của dimensions/metrics
```json
{
  "property_id": "123456789",
  "dimensions": "country,city",
  "metrics": "sessions,users"
}
```

### 7. **get_popular_pages_tool**
Lấy trang phổ biến nhất
```json
{
  "property_id": "123456789",
  "start_date": "7daysAgo",
  "end_date": "today",
  "limit": 20
}
```

### 8. **get_traffic_sources_tool**
Lấy thông tin traffic sources
```json
{
  "property_id": "123456789",
  "start_date": "30daysAgo",
  "end_date": "today",
  "limit": 20
}
```

### 9. **health_check**
Health check endpoint
```json
{}
```

## 🔐 Authentication

Server sử dụng **Bearer Token Authentication**:
- Google OAuth2 access token được truyền qua `Authorization: Bearer <token>` header
- Token được extract tự động từ HTTP request
- Không có caching issues - token fresh mỗi lần gọi

### Lấy Access Token
```python
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Refresh token nếu cần
if creds and creds.expired and creds.refresh_token:
    creds.refresh(Request())
    access_token = creds.token
```

## 📊 Response Format

Tất cả tools trả về JSON với format:
```json
{
  "property_id": "123456789",
  "date_range": {
    "start_date": "30daysAgo",
    "end_date": "today"
  },
  "dimensions": ["country", "city"],
  "metrics": ["sessions", "users"],
  "rows": [
    {
      "country": "Vietnam",
      "city": "Ho Chi Minh City",
      "sessions": "1000",
      "users": "800"
    }
  ],
  "totals": [
    {
      "sessions": "5000",
      "users": "4000"
    }
  ],
  "row_count": 100
}
```

## 🔍 Troubleshooting

### Import Errors:
```bash
❌ ImportError: No module named 'mcp.server.fastmcp'
✅ Solution: pip install mcp>=1.0.0
```

### Google Analytics API Errors:
```bash
❌ Google Analytics API error: PERMISSION_DENIED
✅ Solution: Kiểm tra OAuth2 credentials và GA4 property access
```

### Transport Errors:
```bash
❌ SSE transport failed
✅ Solution: Server tự động fallback sang HTTP transport
```

## 📚 Tài liệu tham khảo

- **MCP Python SDK**: https://github.com/modelcontextprotocol/python-sdk
- **Google Analytics Data API**: https://developers.google.com/analytics/devguides/reporting/data/v1
- **GA4 Dimensions & Metrics**: https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 🎉 Kết luận

Server đã được cập nhật để tuân thủ **MCP Python SDK chính thức**:
- ✅ Sử dụng `mcp.server.fastmcp.FastMCP`
- ✅ Xóa manual tools listing
- ✅ Sử dụng Streamable HTTP transport
- ✅ Hỗ trợ SSE fallback
- ✅ Đầy đủ chức năng Google Analytics 4 API
- ✅ Implement missing pivot report tool
- ✅ Bearer token authentication
- ✅ Comprehensive error handling
