# Google Calendar MCP Server

MCP Server cho Google Calendar API v3 sử dụng **FastMCP framework**.

## 🎯 Tính năng

### ✅ Đã được sửa theo chuẩn MCP:
- **Sử dụng `fastmcp.FastMCP`** - Framework nhất quán với project
- **Thêm SCOPES definition** - Đ<PERSON><PERSON> nghĩa đầy đủ Google Calendar scopes
- **Sửa deprecated credentials** - Sử dụng `Credentials(token=access_token)` thay vì `AccessTokenCredentials`
- **Xóa import không cần thiết** - Loại bỏ `import httpx`
- **Thêm transport fallback** - HTTP + SSE fallback mechanism
- **Thêm health check tool** - Endpoint kiểm tra trạng thái server
- **Bearer token authentication** - Extract token từ request headers

### 📅 Google Calendar API v3 Features:
- **Calendar Management** - Tạo, sửa, xóa, list calendars
- **Event Management** - <PERSON><PERSON><PERSON>, sử<PERSON>, x<PERSON><PERSON>, list events
- **Quick Add Events** - Tạo events từ text tự nhiên
- **Freebusy Queries** - Kiểm tra lịch trống/bận
- **Recurring Events** - Hỗ trợ events lặp lại
- **Attendees Management** - Quản lý người tham gia
- **Time Zone Support** - Hỗ trợ múi giờ

## 🔧 Cài đặt

### Dependencies
```bash
pip install google-api-python-client google-auth fastmcp
```

### Environment Variables
```env
# === SERVER CONFIGURATION ===
GOOGLE_CALENDAR_HTTP_HOST=127.0.0.1
GOOGLE_CALENDAR_HTTP_PORT=8019
GOOGLE_CALENDAR_HTTP_PATH=/mcp

# === SSE FALLBACK CONFIGURATION ===
GOOGLE_CALENDAR_SSE_HOST=127.0.0.1
GOOGLE_CALENDAR_SSE_PORT=8119
GOOGLE_CALENDAR_SSE_PATH=/sse

# === TRANSPORT TYPE ===
GOOGLE_CALENDAR_TRANSPORT=http  # hoặc "sse"
```

## 🚀 Chạy Server

```bash
python src/server/redai_system/google/google_calendar_server.py
```

Server sẽ khởi động với:
- **HTTP Transport**: `http://127.0.0.1:8019/mcp`
- **SSE Fallback**: `http://127.0.0.1:8119/sse` (nếu HTTP fail)
- **Health Check**: `http://127.0.0.1:8019/health`

## 🛠️ Tools Available (13 tools)

### 1. **Calendar Management (5 tools)**
- `list_calendars_tool` - List tất cả calendars
- `get_calendar_tool` - Lấy thông tin calendar
- `create_calendar_tool` - Tạo calendar mới
- `update_calendar_tool` - Update calendar
- `delete_calendar_tool` - Xóa calendar

### 2. **Event Management (7 tools)**
- `list_events_tool` - List events trong calendar
- `get_event_tool` - Lấy thông tin event
- `create_event_tool` - Tạo event mới
- `update_event_tool` - Update event
- `delete_event_tool` - Xóa event
- `quick_add_event_tool` - Tạo event nhanh từ text
- `freebusy_query_tool` - Query freebusy information

### 3. **System (1 tool)**
- `health_check` - Health check endpoint

## 🔐 Authentication

Server sử dụng **Bearer Token Authentication**:
- Google OAuth2 access token được truyền qua `Authorization: Bearer <token>` header
- Token được extract tự động từ HTTP request
- Không có caching issues - token fresh mỗi lần gọi

### Required Scopes
```
https://www.googleapis.com/auth/calendar
https://www.googleapis.com/auth/calendar.events
https://www.googleapis.com/auth/calendar.readonly
```

### Lấy Access Token
```python
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Refresh token nếu cần
if creds and creds.expired and creds.refresh_token:
    creds.refresh(Request())
    access_token = creds.token
```

## 📊 Response Format

Tất cả tools trả về JSON với format:
```json
{
  "kind": "calendar#calendar",
  "etag": "\"**********\"",
  "id": "primary",
  "summary": "My Calendar",
  "description": "Calendar description",
  "timeZone": "Asia/Ho_Chi_Minh",
  "colorId": "1"
}
```

## 🔧 Các Sửa Chữa Đã Thực Hiện

### ❌ **Lỗi đã sửa:**

1. **SCOPES Undefined**
   - ❌ Trước: `for scope in SCOPES:` nhưng SCOPES chưa định nghĩa
   - ✅ Sau: Thêm đầy đủ SCOPES definition

2. **Deprecated Credentials**
   - ❌ Trước: `credentials.AccessTokenCredentials(access_token)`
   - ✅ Sau: `Credentials(token=access_token)`

3. **Unused Import**
   - ❌ Trước: `import httpx` không sử dụng
   - ✅ Sau: Xóa import không cần thiết

4. **Missing Transport Fallback**
   - ❌ Trước: Chỉ có HTTP transport
   - ✅ Sau: HTTP + SSE fallback mechanism

5. **Missing Health Check**
   - ❌ Trước: Không có health check endpoint
   - ✅ Sau: Thêm `health_check` tool

6. **Variable Scope Error**
   - ❌ Trước: `UnboundLocalError` với TRANSPORT_TYPE
   - ✅ Sau: Sử dụng local variable copy

## 📚 Tài liệu tham khảo

- **Google Calendar API v3**: https://developers.google.com/calendar/api/v3/reference
- **FastMCP Framework**: https://github.com/jlowin/fastmcp
- **Google Auth Library**: https://google-auth.readthedocs.io/
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 🎉 Kết luận

Google Calendar server đã được cập nhật để:
- ✅ Sử dụng FastMCP framework nhất quán
- ✅ Sửa tất cả deprecated APIs
- ✅ Thêm transport fallback mechanism
- ✅ Thêm health check endpoint
- ✅ Đầy đủ chức năng Google Calendar API v3
- ✅ Bearer token authentication
- ✅ Comprehensive error handling
- ✅ Consistent với các server khác trong project

**Server đã sẵn sàng để sử dụng và tuân thủ hoàn toàn FastMCP framework!** 🎯
