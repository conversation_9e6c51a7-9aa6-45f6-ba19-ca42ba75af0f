# Google Gmail MCP Server

MCP Server cho Gmail API v1 sử dụng **FastMCP framework**.

## 🎯 Tính năng

### ✅ Đã được sửa theo chuẩn MCP:
- **Sử dụng `fastmcp.FastMCP`** - Framework nhất quán với project ✅ (đã đúng)
- **SCOPES definition** - Đ<PERSON><PERSON> nghĩa đầy đủ Gmail scopes ✅ (đã có sẵn)
- **Sửa deprecated credentials** - Sử dụng `Credentials(token=access_token)` thay vì `AccessTokenCredentials`
- **Xóa import không cần thiết** - Loại bỏ `import httpx`
- **Thêm transport fallback** - HTTP + SSE fallback mechanism
- **Thêm health check tool** - Endpoint kiểm tra trạng thái server
- **Bearer token authentication** - Extract token từ request headers ✅ (đã đúng)

### 📧 Gmail API v1 Features:
- **Email Management** - Send, receive, read emails
- **Draft Management** - Create, edit, send drafts
- **Label Management** - Create, list, modify labels
- **Email Search** - Advanced search với filters
- **Thread Management** - Quản lý email threads
- **Attachment Support** - Đọc và download attachments
- **CC/BCC Support** - Hỗ trợ CC và BCC

## 🔧 Cài đặt

### Dependencies
```bash
pip install google-api-python-client google-auth fastmcp
```

### Environment Variables
```env
# === SERVER CONFIGURATION ===
GOOGLE_GMAIL_HTTP_HOST=127.0.0.1
GOOGLE_GMAIL_HTTP_PORT=8018
GOOGLE_GMAIL_HTTP_PATH=/mcp

# === SSE FALLBACK CONFIGURATION ===
GOOGLE_GMAIL_SSE_HOST=127.0.0.1
GOOGLE_GMAIL_SSE_PORT=8118
GOOGLE_GMAIL_SSE_PATH=/sse

# === TRANSPORT TYPE ===
GOOGLE_GMAIL_TRANSPORT=http  # hoặc "sse"
```

## 🚀 Chạy Server

```bash
python src/server/redai_system/google/google_gmail_server.py
```

Server sẽ khởi động với:
- **HTTP Transport**: `http://127.0.0.1:8018/mcp`
- **SSE Fallback**: `http://127.0.0.1:8118/sse` (nếu HTTP fail)
- **Health Check**: `http://127.0.0.1:8018/health`

## 🛠️ Tools Available (10 tools)

### 1. **Email Management (3 tools)**
- `send_email_tool` - Gửi email với CC/BCC support
- `list_emails_tool` - List emails với query filters
- `get_email_tool` - Lấy thông tin chi tiết email

### 2. **Draft Management (2 tools)**
- `create_draft_tool` - Tạo draft email
- `send_draft_tool` - Gửi draft đã tạo

### 3. **Label Management (3 tools)**
- `list_labels_tool` - List tất cả labels
- `create_label_tool` - Tạo label mới
- `modify_email_labels_tool` - Thay đổi labels của email

### 4. **Search (1 tool)**
- `search_emails_tool` - Tìm kiếm emails nâng cao

### 5. **System (1 tool)**
- `health_check` - Health check endpoint ✨ (mới thêm)

## 🔐 Authentication

Server sử dụng **Bearer Token Authentication**:
- Google OAuth2 access token được truyền qua `Authorization: Bearer <token>` header
- Token được extract tự động từ HTTP request
- Không có caching issues - token fresh mỗi lần gọi

### Required Scopes
```
https://www.googleapis.com/auth/gmail.readonly
https://www.googleapis.com/auth/gmail.send
https://www.googleapis.com/auth/gmail.modify
https://www.googleapis.com/auth/gmail.compose
```

### Lấy Access Token
```python
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Refresh token nếu cần
if creds and creds.expired and creds.refresh_token:
    creds.refresh(Request())
    access_token = creds.token
```

## 📊 Response Format

Tất cả tools trả về JSON với format:
```json
{
  "messages": [
    {
      "id": "17c9b9e1a2b3c4d5",
      "threadId": "17c9b9e1a2b3c4d5",
      "labelIds": ["INBOX", "UNREAD"],
      "snippet": "Email content preview...",
      "payload": {
        "headers": [
          {"name": "From", "value": "<EMAIL>"},
          {"name": "Subject", "value": "Email Subject"}
        ]
      }
    }
  ],
  "nextPageToken": "next_page_token_here",
  "resultSizeEstimate": 1
}
```

## 🔧 Các Sửa Chữa Đã Thực Hiện

### ❌ **Lỗi đã sửa:**

1. **Deprecated Credentials**
   - ❌ Trước: `credentials.AccessTokenCredentials(access_token)`
   - ✅ Sau: `Credentials(token=access_token)`

2. **Unused Import**
   - ❌ Trước: `import httpx` không sử dụng
   - ✅ Sau: Xóa import không cần thiết

3. **Missing Transport Fallback**
   - ❌ Trước: Chỉ có HTTP transport
   - ✅ Sau: HTTP + SSE fallback mechanism

4. **Missing Health Check**
   - ❌ Trước: Không có health check endpoint
   - ✅ Sau: Thêm `health_check` tool với features list

### ✅ **Đã đúng từ đầu:**
- Framework FastMCP
- SCOPES definition
- Bearer token authentication
- Tool structure

## 📚 Tài liệu tham khảo

- **Gmail API v1**: https://developers.google.com/gmail/api/reference/rest
- **FastMCP Framework**: https://github.com/jlowin/fastmcp
- **Google Auth Library**: https://google-auth.readthedocs.io/
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 💡 Ví dụ sử dụng

### Gửi email
```json
{
  "tool": "send_email_tool",
  "arguments": {
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "Hello World!",
    "cc": "<EMAIL>",
    "bcc": "<EMAIL>"
  }
}
```

### Tìm kiếm emails
```json
{
  "tool": "search_emails_tool",
  "arguments": {
    "search_term": "important",
    "from_email": "<EMAIL>",
    "has_attachment": true,
    "is_unread": true,
    "max_results": 10
  }
}
```

### Tạo và gửi draft
```json
{
  "tool": "create_draft_tool",
  "arguments": {
    "to": "<EMAIL>",
    "subject": "Draft Email",
    "body": "This is a draft email."
  }
}
```

### Quản lý labels
```json
{
  "tool": "modify_email_labels_tool",
  "arguments": {
    "message_id": "17c9b9e1a2b3c4d5",
    "add_label_ids": "Label_1,Label_2",
    "remove_label_ids": "UNREAD"
  }
}
```

## 🔍 Gmail Search Syntax

Server hỗ trợ Gmail search syntax:
- `from:<EMAIL>` - Emails từ người gửi cụ thể
- `to:<EMAIL>` - Emails gửi đến người nhận cụ thể
- `subject:keyword` - Emails có keyword trong subject
- `has:attachment` - Emails có attachment
- `is:unread` - Emails chưa đọc
- `is:starred` - Emails đã đánh dấu sao
- `label:labelname` - Emails có label cụ thể

## 🎉 Kết luận

Google Gmail server đã được cập nhật để:
- ✅ Sử dụng FastMCP framework nhất quán
- ✅ Sửa tất cả deprecated APIs
- ✅ Thêm transport fallback mechanism
- ✅ Thêm health check endpoint
- ✅ Đầy đủ chức năng Gmail API v1
- ✅ Bearer token authentication
- ✅ Comprehensive error handling
- ✅ Advanced email search capabilities
- ✅ Draft management features
- ✅ Label management system
- ✅ Consistent với các server khác trong project

**Server đã sẵn sàng để sử dụng và tuân thủ hoàn toàn FastMCP framework!** 🎯
