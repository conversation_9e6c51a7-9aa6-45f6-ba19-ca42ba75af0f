#!/usr/bin/env python3
"""
Google Docs MCP Server sử dụng FastMCP và Google Docs API v1

Server này cung cấp các MCP tools để tương tác với Google Docs API,
bao gồm tạo, đọc và chỉnh sửa documents.

Tính năng:
- Tạo documents mới
- Đọc nội dung documents
- Batch update documents (thêm text, format, images, etc.)
- Quản lý document structure
- Text formatting và styling

Transport: Streamable HTTP
Authentication: Google OAuth2 với access token từ client headers
"""

import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Union
from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request
from google.oauth2 import credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from datetime import datetime

# Google Docs API scopes
SCOPES = [
    'https://www.googleapis.com/auth/documents',
    'https://www.googleapis.com/auth/documents.readonly',
    'https://www.googleapis.com/auth/drive.file'
]

# Cấu hình server
HTTP_HOST = os.getenv("GOOGLE_DOCS_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_DOCS_HTTP_PORT", "8016"))
HTTP_PATH = os.getenv("GOOGLE_DOCS_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("GOOGLE_DOCS_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GOOGLE_DOCS_SSE_PORT", "8116"))
SSE_PATH = os.getenv("GOOGLE_DOCS_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("GOOGLE_DOCS_TRANSPORT", "http").lower()  # http hoặc sse

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

class GoogleDocsClient:
    """Client để tương tác với Google Docs API"""

    def __init__(self):
        # Không khởi tạo service cố định, sẽ tạo mới mỗi lần với token từ request
        pass

    def _create_service_with_token(self, access_token: str):
        """Tạo Google Docs service với access token từ client"""
        try:
            # Tạo credentials từ access token
            creds = credentials.AccessTokenCredentials(access_token)

            # Tạo service với credentials
            service = build('docs', 'v1', credentials=creds)
            return service

        except Exception as e:
            raise Exception(f"Lỗi tạo Google Docs service với token: {str(e)}")

    def _get_service(self):
        """Lấy service với token từ request headers"""
        token = extract_bearer_token_from_request()
        if not token:
            raise Exception("Không tìm thấy Bearer token trong request headers. Vui lòng cung cấp Google OAuth2 access token.")

        return self._create_service_with_token(token)

    async def create_document(self, title: str) -> Dict:
        """Tạo document mới"""
        service = self._get_service()

        body = {
            'title': title
        }

        try:
            result = service.documents().create(body=body).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi tạo document: {str(e)}")

    async def get_document(self, document_id: str) -> Dict:
        """Lấy thông tin document"""
        service = self._get_service()

        try:
            result = service.documents().get(documentId=document_id).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi lấy document: {str(e)}")

    async def batch_update_document(self, document_id: str, requests: List[Dict]) -> Dict:
        """Batch update document với các requests"""
        service = self._get_service()

        body = {
            'requests': requests
        }

        try:
            result = service.documents().batchUpdate(
                documentId=document_id,
                body=body
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi batch update document: {str(e)}")

# Khởi tạo Google Docs client
docs_client = GoogleDocsClient()

# Khởi tạo MCP server
mcp = FastMCP("Google-Docs-Server")

# FastMCP tự động handle tools/list từ các @mcp.tool() decorators
# Không cần implement list_tools manually

@mcp.tool(description="Tạo Google Docs document mới")
async def create_document_tool(
    ctx,
    title: str
) -> str:
    """
    Tạo Google Document mới

    Args:
        title: Tên của document

    Returns:
        JSON string chứa thông tin document đã tạo
    """
    try:
        # Extract Bearer token từ request headers
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🔑 Sử dụng Bearer token cho Google Docs API: {token[:20]}...")
        await ctx.info(f"📄 Tạo document với title: {title}")

        result = await docs_client.create_document(title)

        # Log thông tin document đã tạo
        document_id = result.get('documentId', 'Unknown')
        document_url = f"https://docs.google.com/document/d/{document_id}/edit"
        await ctx.info(f"✅ Đã tạo document thành công - ID: {document_id}")
        await ctx.info(f"🔗 URL: {document_url}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo document: {str(e)}")
        return f"❌ Lỗi tạo document: {str(e)}"

@mcp.tool(description="Lấy nội dung document")
async def get_document_info_tool(
    ctx,
    document_id: str
) -> str:
    """
    Lấy thông tin chi tiết về document

    Args:
        document_id: ID của document

    Returns:
        JSON string chứa thông tin document
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📄 Lấy thông tin document: {document_id}")

        result = await docs_client.get_document(document_id)

        # Log thông tin cơ bản
        title = result.get('title', 'Unknown')
        revision_id = result.get('revisionId', 'Unknown')
        await ctx.info(f"✅ Đã lấy thông tin document: {title} (revision: {revision_id})")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy thông tin document: {str(e)}")
        return f"❌ Lỗi lấy thông tin document: {str(e)}"

@mcp.tool(description="Insert text vào document")
async def insert_text_tool(
    ctx,
    document_id: str,
    text: str,
    index: int = 1
) -> str:
    """
    Chèn text vào document tại vị trí chỉ định

    Args:
        document_id: ID của document
        text: Text cần chèn
        index: Vị trí chèn (mặc định 1 - đầu document)

    Returns:
        JSON string chứa kết quả insert
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"✏️ Chèn text vào document {document_id} tại index {index}")
        await ctx.info(f"📝 Text: {text[:100]}{'...' if len(text) > 100 else ''}")

        requests = [{
            'insertText': {
                'location': {
                    'index': index
                },
                'text': text
            }
        }]

        result = await docs_client.batch_update_document(document_id, requests)

        await ctx.info(f"✅ Đã chèn {len(text)} ký tự thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi chèn text: {str(e)}")
        return f"❌ Lỗi chèn text: {str(e)}"

@mcp.tool(description="Replace text trong document")
async def replace_text_tool(
    ctx,
    document_id: str,
    old_text: str,
    new_text: str,
    match_case: bool = False
) -> str:
    """
    Thay thế text trong document

    Args:
        document_id: ID của document
        old_text: Text cần thay thế
        new_text: Text mới
        match_case: Có phân biệt hoa thường không (mặc định False)

    Returns:
        JSON string chứa kết quả replace
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🔄 Thay thế text trong document {document_id}")
        await ctx.info(f"📝 Từ: '{old_text}' -> Thành: '{new_text}'")

        requests = [{
            'replaceAllText': {
                'containsText': {
                    'text': old_text,
                    'matchCase': match_case
                },
                'replaceText': new_text
            }
        }]

        result = await docs_client.batch_update_document(document_id, requests)

        # Log số lượng thay thế
        replacements = result.get('replies', [{}])[0].get('replaceAllText', {}).get('occurrencesChanged', 0)
        await ctx.info(f"✅ Đã thay thế {replacements} lần xuất hiện")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi thay thế text: {str(e)}")
        return f"❌ Lỗi thay thế text: {str(e)}"

@mcp.tool()
async def delete_text_tool(
    ctx,
    document_id: str,
    start_index: int,
    end_index: int
) -> str:
    """
    Xóa text trong document từ start_index đến end_index

    Args:
        document_id: ID của document
        start_index: Vị trí bắt đầu xóa
        end_index: Vị trí kết thúc xóa

    Returns:
        JSON string chứa kết quả delete
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🗑️ Xóa text trong document {document_id} từ {start_index} đến {end_index}")

        requests = [{
            'deleteContentRange': {
                'range': {
                    'startIndex': start_index,
                    'endIndex': end_index
                }
            }
        }]

        result = await docs_client.batch_update_document(document_id, requests)

        deleted_chars = end_index - start_index
        await ctx.info(f"✅ Đã xóa {deleted_chars} ký tự thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi xóa text: {str(e)}")
        return f"❌ Lỗi xóa text: {str(e)}"

@mcp.tool()
async def format_text_tool(
    ctx,
    document_id: str,
    start_index: int,
    end_index: int,
    format_json: str
) -> str:
    """
    Format text trong document

    Args:
        document_id: ID của document
        start_index: Vị trí bắt đầu format
        end_index: Vị trí kết thúc format
        format_json: JSON format object (bold, italic, fontSize, etc.)

    Returns:
        JSON string chứa kết quả format
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🎨 Format text trong document {document_id} từ {start_index} đến {end_index}")

        text_style = json.loads(format_json)

        requests = [{
            'updateTextStyle': {
                'range': {
                    'startIndex': start_index,
                    'endIndex': end_index
                },
                'textStyle': text_style,
                'fields': ','.join(text_style.keys())
            }
        }]

        result = await docs_client.batch_update_document(document_id, requests)

        formatted_chars = end_index - start_index
        await ctx.info(f"✅ Đã format {formatted_chars} ký tự thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        await ctx.error("❌ Lỗi: format_json không đúng định dạng JSON")
        return "❌ Lỗi: format_json không đúng định dạng JSON"
    except Exception as e:
        await ctx.error(f"❌ Lỗi format text: {str(e)}")
        return f"❌ Lỗi format text: {str(e)}"

@mcp.tool()
async def insert_page_break_tool(
    ctx,
    document_id: str,
    index: int
) -> str:
    """
    Chèn page break vào document

    Args:
        document_id: ID của document
        index: Vị trí chèn page break

    Returns:
        JSON string chứa kết quả insert
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📄 Chèn page break vào document {document_id} tại index {index}")

        requests = [{
            'insertPageBreak': {
                'location': {
                    'index': index
                }
            }
        }]

        result = await docs_client.batch_update_document(document_id, requests)

        await ctx.info(f"✅ Đã chèn page break thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi chèn page break: {str(e)}")
        return f"❌ Lỗi chèn page break: {str(e)}"

@mcp.tool()
async def insert_table_tool(
    ctx,
    document_id: str,
    index: int,
    rows: int,
    columns: int
) -> str:
    """
    Chèn table vào document

    Args:
        document_id: ID của document
        index: Vị trí chèn table
        rows: Số hàng
        columns: Số cột

    Returns:
        JSON string chứa kết quả insert
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📊 Chèn table {rows}x{columns} vào document {document_id} tại index {index}")

        requests = [{
            'insertTable': {
                'location': {
                    'index': index
                },
                'rows': rows,
                'columns': columns
            }
        }]

        result = await docs_client.batch_update_document(document_id, requests)

        await ctx.info(f"✅ Đã chèn table {rows}x{columns} thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi chèn table: {str(e)}")
        return f"❌ Lỗi chèn table: {str(e)}"

@mcp.tool()
async def batch_update_document_tool(
    ctx,
    document_id: str,
    requests_json: str
) -> str:
    """
    Batch update document với nhiều requests tùy chỉnh

    Args:
        document_id: ID của document
        requests_json: JSON array chứa các request objects

    Returns:
        JSON string chứa kết quả batch update
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        requests = json.loads(requests_json)
        await ctx.info(f"🔄 Batch update document {document_id} với {len(requests)} requests")

        result = await docs_client.batch_update_document(document_id, requests)

        await ctx.info(f"✅ Đã thực hiện {len(requests)} requests thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        await ctx.error("❌ Lỗi: requests_json không đúng định dạng JSON")
        return "❌ Lỗi: requests_json không đúng định dạng JSON"
    except Exception as e:
        await ctx.error(f"❌ Lỗi batch update: {str(e)}")
        return f"❌ Lỗi batch update: {str(e)}"

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động Google Docs MCP Server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path=HTTP_PATH
            )

        elif transport == "sse":
            print(f"🚀 Khởi động Google Docs MCP Server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động Google Docs MCP Server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def main():
    """Hàm main để chạy server"""
    try:
        print("=" * 70)
        print("📄 Khởi động Google Docs MCP Server")
        print("=" * 70)
        print("📋 Transport: Streamable HTTP (FastMCP)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print("🔧 Google Docs API: v1")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Google OAuth2 access token trong Authorization header")
        print()

        print("=" * 70)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()