# Google Docs MCP Server

MCP Server cho Google Docs API v1 sử dụng **FastMCP framework**.

## 🎯 Tính năng

### ✅ Đ<PERSON> được sửa theo chuẩn MCP:
- **Sử dụng `fastmcp.FastMCP`** - Framework nhất quán với project
- **Thêm SCOPES definition** - Đ<PERSON><PERSON> nghĩa đầy đủ Google Docs scopes
- **Sửa deprecated credentials** - Sử dụng `Credentials(token=access_token)` thay vì `AccessTokenCredentials`
- **Xóa import không cần thiết** - Loại bỏ `import httpx`
- **Thêm transport fallback** - HTTP + SSE fallback mechanism
- **Thêm health check tool** - Endpoint kiểm tra trạng thái server
- **Bearer token authentication** - Extract token từ request headers

### 📄 Google Docs API v1 Features:
- **Document Creation** - Tạo documents mới
- **Document Reading** - Đ<PERSON>c nội dung documents
- **Text Manipulation** - Insert, replace, delete text
- **Text Formatting** - Bold, italic, font size, colors
- **Document Structure** - Page breaks, tables
- **Batch Operations** - Multiple updates trong một request

## 🔧 Cài đặt

### Dependencies
```bash
pip install google-api-python-client google-auth fastmcp
```

### Environment Variables
```env
# === SERVER CONFIGURATION ===
GOOGLE_DOCS_HTTP_HOST=127.0.0.1
GOOGLE_DOCS_HTTP_PORT=8016
GOOGLE_DOCS_HTTP_PATH=/mcp

# === SSE FALLBACK CONFIGURATION ===
GOOGLE_DOCS_SSE_HOST=127.0.0.1
GOOGLE_DOCS_SSE_PORT=8116
GOOGLE_DOCS_SSE_PATH=/sse

# === TRANSPORT TYPE ===
GOOGLE_DOCS_TRANSPORT=http  # hoặc "sse"
```

## 🚀 Chạy Server

```bash
python src/server/redai_system/google/google_docs_server.py
```

Server sẽ khởi động với:
- **HTTP Transport**: `http://127.0.0.1:8016/mcp`
- **SSE Fallback**: `http://127.0.0.1:8116/sse` (nếu HTTP fail)
- **Health Check**: `http://127.0.0.1:8016/health`

## 🛠️ Tools Available (10 tools)

### 1. **Document Management (2 tools)**
- `create_document_tool` - Tạo document mới
- `get_document_info_tool` - Lấy thông tin document

### 2. **Text Operations (3 tools)**
- `insert_text_tool` - Chèn text vào document
- `replace_text_tool` - Thay thế text trong document
- `delete_text_tool` - Xóa text trong document

### 3. **Formatting (1 tool)**
- `format_text_tool` - Format text (bold, italic, font size, etc.)

### 4. **Document Structure (2 tools)**
- `insert_page_break_tool` - Chèn page break
- `insert_table_tool` - Chèn table

### 5. **Advanced Operations (1 tool)**
- `batch_update_document_tool` - Batch update với nhiều requests

### 6. **System (1 tool)**
- `health_check` - Health check endpoint ✨ (mới thêm)

## 🔐 Authentication

Server sử dụng **Bearer Token Authentication**:
- Google OAuth2 access token được truyền qua `Authorization: Bearer <token>` header
- Token được extract tự động từ HTTP request
- Không có caching issues - token fresh mỗi lần gọi

### Required Scopes
```
https://www.googleapis.com/auth/documents
https://www.googleapis.com/auth/documents.readonly
https://www.googleapis.com/auth/drive.file
```

### Lấy Access Token
```python
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Refresh token nếu cần
if creds and creds.expired and creds.refresh_token:
    creds.refresh(Request())
    access_token = creds.token
```

## 📊 Response Format

Tất cả tools trả về JSON với format:
```json
{
  "documentId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "title": "Example Document",
  "body": {
    "content": [
      {
        "paragraph": {
          "elements": [
            {
              "textRun": {
                "content": "Hello World!\n"
              }
            }
          ]
        }
      }
    ]
  },
  "revisionId": "ALm37BVTnku9DA"
}
```

## 🔧 Các Sửa Chữa Đã Thực Hiện

### ❌ **Lỗi đã sửa:**

1. **Deprecated Credentials**
   - ❌ Trước: `credentials.AccessTokenCredentials(access_token)`
   - ✅ Sau: `Credentials(token=access_token)`

2. **Unused Import**
   - ❌ Trước: `import httpx` không sử dụng
   - ✅ Sau: Xóa import không cần thiết

3. **Missing SCOPES**
   - ❌ Trước: Không có SCOPES definition
   - ✅ Sau: Thêm đầy đủ Google Docs scopes

4. **Missing Transport Fallback**
   - ❌ Trước: Chỉ có HTTP transport
   - ✅ Sau: HTTP + SSE fallback mechanism

5. **Missing Health Check**
   - ❌ Trước: Không có health check endpoint
   - ✅ Sau: Thêm `health_check` tool với features list

## 📚 Tài liệu tham khảo

- **Google Docs API v1**: https://developers.google.com/docs/api/reference/rest
- **FastMCP Framework**: https://github.com/jlowin/fastmcp
- **Google Auth Library**: https://google-auth.readthedocs.io/
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 💡 Ví dụ sử dụng

### Tạo document mới
```json
{
  "tool": "create_document_tool",
  "arguments": {
    "title": "My New Document"
  }
}
```

### Chèn text
```json
{
  "tool": "insert_text_tool",
  "arguments": {
    "document_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "text": "Hello World!",
    "index": 1
  }
}
```

### Format text
```json
{
  "tool": "format_text_tool",
  "arguments": {
    "document_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "start_index": 1,
    "end_index": 12,
    "format_json": "{\"bold\": true, \"fontSize\": {\"magnitude\": 14, \"unit\": \"PT\"}}"
  }
}
```

### Chèn table
```json
{
  "tool": "insert_table_tool",
  "arguments": {
    "document_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "index": 1,
    "rows": 3,
    "columns": 2
  }
}
```

## 🎉 Kết luận

Google Docs server đã được cập nhật để:
- ✅ Sử dụng FastMCP framework nhất quán
- ✅ Sửa tất cả deprecated APIs
- ✅ Thêm transport fallback mechanism
- ✅ Thêm health check endpoint
- ✅ Đầy đủ chức năng Google Docs API v1
- ✅ Bearer token authentication
- ✅ Comprehensive error handling
- ✅ Consistent với các server khác trong project

**Server đã sẵn sàng để sử dụng và tuân thủ hoàn toàn FastMCP framework!** 🎯
