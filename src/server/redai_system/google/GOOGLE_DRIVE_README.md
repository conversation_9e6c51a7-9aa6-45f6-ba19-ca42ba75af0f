# Google Drive MCP Server

MCP Server cho Google Drive API v3 sử dụng **FastMCP framework**.

## 🎯 Tính năng

### ✅ Đ<PERSON> được sửa theo chuẩn MCP:
- **Sử dụng `fastmcp.FastMCP`** - Framework nhất quán với project
- **Xóa import sai** - Loại bỏ `from mcp.types import Tool`
- **Thêm SCOPES definition** - Định nghĩa đầy đủ Google Drive scopes
- **Sửa deprecated credentials** - Sử dụng `Credentials(token=access_token)` thay vì `AccessTokenCredentials`
- **Thêm transport fallback** - HTTP + SSE fallback mechanism
- **Thêm health check tool** - Endpoint kiểm tra trạng thái server
- **Bearer token authentication** - Extract token từ request headers

### 💾 Google Drive API v3 Features:
- **File Management** - Create, read, update, delete files
- **Folder Management** - Create, organize folders
- **File Operations** - Copy, move, search files
- **Content Management** - Upload, download file content
- **Metadata Management** - File properties, permissions
- **Advanced Search** - Query với filters phức tạp

## 🔧 Cài đặt

### Dependencies
```bash
pip install google-api-python-client google-auth fastmcp
```

### Environment Variables
```env
# === SERVER CONFIGURATION ===
GOOGLE_DRIVE_HTTP_HOST=127.0.0.1
GOOGLE_DRIVE_HTTP_PORT=8017
GOOGLE_DRIVE_HTTP_PATH=/mcp

# === SSE FALLBACK CONFIGURATION ===
GOOGLE_DRIVE_SSE_HOST=127.0.0.1
GOOGLE_DRIVE_SSE_PORT=8117
GOOGLE_DRIVE_SSE_PATH=/sse

# === TRANSPORT TYPE ===
GOOGLE_DRIVE_TRANSPORT=http  # hoặc "sse"
```

## 🚀 Chạy Server

```bash
python src/server/redai_system/google/google_drive_server.py
```

Server sẽ khởi động với:
- **HTTP Transport**: `http://127.0.0.1:8017/mcp`
- **SSE Fallback**: `http://127.0.0.1:8117/sse` (nếu HTTP fail)
- **Health Check**: `http://127.0.0.1:8017/health`

## 🛠️ Tools Available (9 tools)

### 1. **File Management (4 tools)**
- `list_files_tool` - List files với query filters
- `get_file_info_tool` - Lấy thông tin chi tiết file
- `create_file_tool` - Tạo file mới với content
- `update_file_tool` - Update file content và metadata

### 2. **Folder Management (1 tool)**
- `create_folder_tool` - Tạo folder mới

### 3. **File Operations (2 tools)**
- `delete_file_tool` - Xóa file/folder
- `copy_file_tool` - Copy file

### 4. **Search (1 tool)**
- `search_files_tool` - Tìm kiếm files với advanced queries

### 5. **System (1 tool)**
- `health_check` - Health check endpoint ✨ (mới thêm)

## 🔐 Authentication

Server sử dụng **Bearer Token Authentication**:
- Google OAuth2 access token được truyền qua `Authorization: Bearer <token>` header
- Token được extract tự động từ HTTP request
- Không có caching issues - token fresh mỗi lần gọi

### Required Scopes
```
https://www.googleapis.com/auth/drive
https://www.googleapis.com/auth/drive.file
https://www.googleapis.com/auth/drive.readonly
https://www.googleapis.com/auth/drive.metadata
https://www.googleapis.com/auth/drive.metadata.readonly
```

### Lấy Access Token
```python
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

# Refresh token nếu cần
if creds and creds.expired and creds.refresh_token:
    creds.refresh(Request())
    access_token = creds.token
```

## 📊 Response Format

Tất cả tools trả về JSON với format:
```json
{
  "files": [
    {
      "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
      "name": "Example File",
      "mimeType": "application/vnd.google-apps.document",
      "size": "1024",
      "createdTime": "2024-01-01T10:00:00.000Z",
      "modifiedTime": "2024-01-01T11:00:00.000Z",
      "webViewLink": "https://docs.google.com/document/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit",
      "parents": ["0BwwA4oUTeiV1TGRPeTVjaWRDY1E"]
    }
  ],
  "nextPageToken": "next_page_token_here"
}
```

## 🔧 Các Sửa Chữa Đã Thực Hiện

### ❌ **Lỗi đã sửa:**

1. **Wrong Import**
   - ❌ Trước: `from mcp.types import Tool` (sai import)
   - ✅ Sau: Xóa import không cần thiết với FastMCP

2. **Deprecated Credentials**
   - ❌ Trước: `credentials.AccessTokenCredentials(access_token)`
   - ✅ Sau: `Credentials(token=access_token)`

3. **Missing SCOPES**
   - ❌ Trước: Không có SCOPES definition
   - ✅ Sau: Thêm đầy đủ Google Drive scopes

4. **Missing Transport Fallback**
   - ❌ Trước: Chỉ có HTTP transport
   - ✅ Sau: HTTP + SSE fallback mechanism

5. **Missing Health Check**
   - ❌ Trước: Không có health check endpoint
   - ✅ Sau: Thêm `health_check` tool với features list

## 📚 Tài liệu tham khảo

- **Google Drive API v3**: https://developers.google.com/drive/api/v3/reference
- **FastMCP Framework**: https://github.com/jlowin/fastmcp
- **Google Auth Library**: https://google-auth.readthedocs.io/
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 💡 Ví dụ sử dụng

### List files
```json
{
  "tool": "list_files_tool",
  "arguments": {
    "query": "name contains 'report'",
    "page_size": 20,
    "order_by": "modifiedTime desc"
  }
}
```

### Tạo file mới
```json
{
  "tool": "create_file_tool",
  "arguments": {
    "name": "My Document.txt",
    "mime_type": "text/plain",
    "content": "Hello World!",
    "parent_id": "folder_id_here"
  }
}
```

### Search files
```json
{
  "tool": "search_files_tool",
  "arguments": {
    "search_term": "budget",
    "file_type": "spreadsheet",
    "in_folder": "folder_id_here",
    "page_size": 10
  }
}
```

### Copy file
```json
{
  "tool": "copy_file_tool",
  "arguments": {
    "file_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "name": "Copy of Document",
    "parent_id": "destination_folder_id"
  }
}
```

## 🔍 Advanced Search Queries

Server hỗ trợ Google Drive query syntax:
- `name contains 'keyword'` - Tìm theo tên
- `mimeType = 'application/vnd.google-apps.folder'` - Chỉ folders
- `'parent_id' in parents` - Files trong folder cụ thể
- `modifiedTime > '2024-01-01T00:00:00'` - Files modified sau ngày
- `trashed = false` - Loại bỏ files đã xóa

## 🎉 Kết luận

Google Drive server đã được cập nhật để:
- ✅ Sử dụng FastMCP framework nhất quán
- ✅ Sửa tất cả deprecated APIs và wrong imports
- ✅ Thêm transport fallback mechanism
- ✅ Thêm health check endpoint
- ✅ Đầy đủ chức năng Google Drive API v3
- ✅ Bearer token authentication
- ✅ Comprehensive error handling
- ✅ Advanced search capabilities
- ✅ Consistent với các server khác trong project

**Server đã sẵn sàng để sử dụng và tuân thủ hoàn toàn FastMCP framework!** 🎯
