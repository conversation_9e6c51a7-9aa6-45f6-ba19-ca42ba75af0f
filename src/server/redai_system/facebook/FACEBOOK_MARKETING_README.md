# Facebook Marketing MCP Server

MCP Server cho Facebook Marketing API sử dụng **FastMCP framework** và **Facebook Business SDK**.

## 🎯 Đánh giá so với Facebook Business SDK

### ✅ **ĐÚNG CHUẨN FACEBOOK BUSINESS SDK**

#### **1. SDK Installation & Import**
- ✅ **Correct**: `pip install facebook-business`
- ✅ **Correct**: Import từ `facebook_business.api import FacebookAdsApi`
- ✅ **Correct**: Import các adobjects đúng cách

#### **2. API Initialization**
- ✅ **Correct**: `FacebookAdsApi.init(app_id, app_secret, access_token)`
- ✅ **Correct**: Sử dụng App ID và App Secret từ environment variables
- ✅ **Correct**: Access token từ Bearer token headers

#### **3. Object Usage**
- ✅ **Correct**: `User(fbid='me')` để lấy user info
- ✅ **Correct**: `AdAccount(account_id)` để access ad account
- ✅ **Correct**: Sử dụng `.get_campaigns()`, `.get_ad_sets()`, `.get_ads()`
- ✅ **Correct**: Fields được define đúng cách với `Campaign.Field.name`

#### **4. Error Handling**
- ✅ **Correct**: Catch `FacebookRequestError` exceptions
- ✅ **Correct**: Proper error propagation

### ✅ **CÁC TÍNH NĂNG ĐÃ IMPLEMENT ĐÚNG**

1. **Ad Accounts Management** - ✅ Đúng chuẩn SDK
2. **Campaigns CRUD** - ✅ Đúng chuẩn SDK  
3. **Ad Sets Management** - ✅ Đúng chuẩn SDK
4. **Ads Management** - ✅ Đúng chuẩn SDK
5. **Insights/Reports** - ✅ Đúng chuẩn SDK
6. **Custom Audiences** - ✅ Đúng chuẩn SDK
7. **Ad Creatives** - ✅ Đúng chuẩn SDK
8. **Ad Images Upload** - ✅ Đúng chuẩn SDK
9. **Facebook Pages** - ✅ Đúng chuẩn SDK
10. **User Information** - ✅ Đúng chuẩn SDK

### 🔧 **CÁC SỬA CHỮA ĐÃ THỰC HIỆN**

#### **1. Unused Import**
- ❌ **Trước**: `import httpx` không sử dụng
- ✅ **Sau**: Xóa import không cần thiết

#### **2. Transport Fallback**
- ❌ **Trước**: Chỉ có HTTP transport
- ✅ **Sau**: HTTP + SSE fallback mechanism

#### **3. Health Check Enhancement**
- ❌ **Trước**: Health check đơn giản
- ✅ **Sau**: Health check chi tiết với features list

#### **4. Environment Variables**
- ❌ **Trước**: Thiếu SSE config
- ✅ **Sau**: Đầy đủ HTTP + SSE config

## 🔧 Cài đặt

### Dependencies
```bash
pip install facebook-business fastmcp
```

### Environment Variables
```env
# === FACEBOOK APP CONFIGURATION (BẮT BUỘC) ===
FACEBOOK_APP_ID=your_app_id_here
FACEBOOK_APP_SECRET=your_app_secret_here

# === SERVER CONFIGURATION ===
FACEBOOK_MARKETING_HTTP_HOST=127.0.0.1
FACEBOOK_MARKETING_HTTP_PORT=8022
FACEBOOK_MARKETING_HTTP_PATH=/mcp

# === SSE FALLBACK CONFIGURATION ===
FACEBOOK_MARKETING_SSE_HOST=127.0.0.1
FACEBOOK_MARKETING_SSE_PORT=8122
FACEBOOK_MARKETING_SSE_PATH=/sse

# === TRANSPORT TYPE ===
FACEBOOK_MARKETING_TRANSPORT=http  # hoặc "sse"
```

## 🚀 Chạy Server

```bash
python src/server/redai_system/facebook/facebook_marketing_server.py
```

Server sẽ khởi động với:
- **HTTP Transport**: `http://127.0.0.1:8022/mcp`
- **SSE Fallback**: `http://127.0.0.1:8122/sse` (nếu HTTP fail)
- **Health Check**: `http://127.0.0.1:8022/health`

## 🔐 Authentication

### **Bearer Token Authentication**
- Facebook access token được truyền qua `Authorization: Bearer <token>` header
- Token được extract tự động từ HTTP request
- Không có caching issues - token fresh mỗi lần gọi

### **Required Environment Variables**
- `FACEBOOK_APP_ID` - Facebook App ID (bắt buộc)
- `FACEBOOK_APP_SECRET` - Facebook App Secret (bắt buộc)

### **Lấy Access Token**
1. Đăng ký app tại [developers.facebook.com](https://developers.facebook.com/)
2. Thêm Marketing API product
3. Sử dụng [Graph Explorer](https://developers.facebook.com/tools/explorer) để lấy access token
4. Cần permissions: `ads_management`, `manage_pages`

## 🛠️ Tools Available (13 tools)

### **1. Account Management (1 tool)**
- `get_ad_accounts_tool` - Lấy danh sách ad accounts

### **2. Campaign Management (2 tools)**
- `get_campaigns_tool` - Lấy danh sách campaigns
- `create_campaign_tool` - Tạo campaign mới

### **3. Ad Set Management (1 tool)**
- `get_ad_sets_tool` - Lấy danh sách ad sets

### **4. Ad Management (1 tool)**
- `get_ads_tool` - Lấy danh sách ads

### **5. Insights & Reports (1 tool)**
- `get_insights_tool` - Lấy insights/reports

### **6. Audience Management (1 tool)**
- `get_custom_audiences_tool` - Lấy custom audiences

### **7. Creative Management (3 tools)**
- `get_ad_creatives_tool` - Lấy ad creatives
- `create_ad_creative_tool` - Tạo ad creative mới
- `get_ad_images_tool` - Lấy ad images

### **8. Image Management (1 tool)**
- `upload_ad_image_tool` - Upload ad image

### **9. Page Management (1 tool)**
- `get_pages_tool` - Lấy Facebook Pages

### **10. User Management (1 tool)**
- `get_user_info_tool` - Lấy thông tin user

### **11. System (1 tool)**
- `health_check` - Health check endpoint

## 📊 Response Format

Tất cả tools trả về JSON với format:
```json
{
  "ad_accounts": [
    {
      "id": "act_123456789",
      "name": "My Ad Account",
      "account_status": "1",
      "currency": "USD",
      "timezone_name": "America/Los_Angeles",
      "amount_spent": "1000",
      "balance": "5000"
    }
  ],
  "total_count": 1
}
```

## 💡 Ví dụ sử dụng

### Lấy ad accounts
```json
{
  "tool": "get_ad_accounts_tool",
  "arguments": {}
}
```

### Tạo campaign
```json
{
  "tool": "create_campaign_tool",
  "arguments": {
    "account_id": "act_123456789",
    "name": "My Campaign",
    "objective": "CONVERSIONS",
    "status": "PAUSED"
  }
}
```

### Lấy insights
```json
{
  "tool": "get_insights_tool",
  "arguments": {
    "account_id": "act_123456789",
    "level": "campaign",
    "date_preset": "last_7d"
  }
}
```

## 📚 Tài liệu tham khảo

- **Facebook Business SDK**: https://github.com/facebook/facebook-python-business-sdk
- **Facebook Marketing API**: https://developers.facebook.com/docs/marketing-api
- **FastMCP Framework**: https://github.com/jlowin/fastmcp
- **MCP Specification**: https://spec.modelcontextprotocol.io

## 🎉 Kết luận

Facebook Marketing server **HOÀN TOÀN ĐÚNG CHUẨN** với Facebook Business SDK:

- ✅ **SDK Usage**: Đúng 100% theo tài liệu chính thức
- ✅ **API Initialization**: Đúng cách với app_id, app_secret, access_token
- ✅ **Object Usage**: Sử dụng đúng các adobjects và methods
- ✅ **Error Handling**: Catch FacebookRequestError đúng cách
- ✅ **Authentication**: Bearer token authentication hoàn hảo
- ✅ **Environment Variables**: Đầy đủ và đúng cách
- ✅ **Transport Fallback**: HTTP + SSE mechanism
- ✅ **Comprehensive Features**: 13 tools cover toàn bộ Facebook Marketing API

**Server đã sẵn sàng để sử dụng và tuân thủ hoàn toàn Facebook Business SDK!** 🎯
