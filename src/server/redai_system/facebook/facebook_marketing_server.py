#!/usr/bin/env python3
"""
Facebook Marketing MCP Server

MCP Server cho Facebook Marketing API sử dụng FastMCP framework với HTTP transport.
Cung cấp các tools để quản lý Facebook Ads campaigns, ad sets, ads, và insights.

Author: RedAI Team
Date: 2024-12-28
"""

import asyncio
import json
import os
import sys
from typing import Optional, Dict, Any, List

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
sys.path.insert(0, project_root)

from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request

# Facebook Business SDK imports
try:
    from facebook_business.api import Facebook<PERSON>ds<PERSON><PERSON>
    from facebook_business.adobjects.adaccount import AdAccount
    from facebook_business.adobjects.campaign import Campaign
    from facebook_business.adobjects.adset import AdSet
    from facebook_business.adobjects.ad import Ad
    from facebook_business.adobjects.adcreative import AdCreative
    from facebook_business.adobjects.customaudience import CustomAudience
    from facebook_business.adobjects.adsinsights import AdsInsights
    from facebook_business.adobjects.adimage import AdImage
    from facebook_business.adobjects.page import Page
    from facebook_business.adobjects.user import User
    from facebook_business.exceptions import FacebookRequestError
except ImportError as e:
    print(f"❌ Lỗi import Facebook Business SDK: {e}")
    print("💡 Cài đặt: pip install facebook-business")
    sys.exit(1)

from datetime import datetime

# Cấu hình server
HTTP_HOST = os.getenv("FACEBOOK_MARKETING_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("FACEBOOK_MARKETING_HTTP_PORT", "8022"))
HTTP_PATH = os.getenv("FACEBOOK_MARKETING_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("FACEBOOK_MARKETING_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("FACEBOOK_MARKETING_SSE_PORT", "8122"))
SSE_PATH = os.getenv("FACEBOOK_MARKETING_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("FACEBOOK_MARKETING_TRANSPORT", "http").lower()  # http hoặc sse

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
        return None
    except Exception:
        return None

class FacebookMarketingClient:
    """Client để tương tác với Facebook Marketing API"""
    
    def __init__(self):
        self.api = None
    
    def _get_api(self, access_token: str):
        """Tạo Facebook API client với access token"""
        try:
            # Initialize Facebook API
            app_id = os.getenv('FACEBOOK_APP_ID', '')
            app_secret = os.getenv('FACEBOOK_APP_SECRET', '')
            
            api = FacebookAdsApi.init(app_id, app_secret, access_token)
            self.api = api
            return api
        except Exception as e:
            raise Exception(f"Lỗi tạo Facebook API client: {str(e)}")
    
    async def get_ad_accounts(self, access_token: str) -> Dict:
        """Lấy danh sách ad accounts"""
        self._get_api(access_token)
        
        try:
            me = User(fbid='me')
            ad_accounts = me.get_ad_accounts(fields=[
                AdAccount.Field.id,
                AdAccount.Field.name,
                AdAccount.Field.account_status,
                AdAccount.Field.currency,
                AdAccount.Field.timezone_name,
                AdAccount.Field.amount_spent,
                AdAccount.Field.balance
            ])
            
            accounts = []
            for account in ad_accounts:
                accounts.append({
                    'id': account.get('id'),
                    'name': account.get('name'),
                    'account_status': str(account.get('account_status')),
                    'currency': account.get('currency'),
                    'timezone_name': account.get('timezone_name'),
                    'amount_spent': account.get('amount_spent'),
                    'balance': account.get('balance')
                })
            
            return {
                'ad_accounts': accounts,
                'total_count': len(accounts)
            }
            
        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")
    
    async def get_campaigns(self, access_token: str, account_id: str) -> Dict:
        """Lấy danh sách campaigns"""
        self._get_api(access_token)
        
        try:
            account = AdAccount(account_id)
            campaigns = account.get_campaigns(fields=[
                Campaign.Field.id,
                Campaign.Field.name,
                Campaign.Field.status,
                Campaign.Field.objective,
                Campaign.Field.created_time,
                Campaign.Field.updated_time,
                Campaign.Field.start_time,
                Campaign.Field.stop_time
            ])
            
            campaign_list = []
            for campaign in campaigns:
                campaign_list.append({
                    'id': campaign.get('id'),
                    'name': campaign.get('name'),
                    'status': campaign.get('status'),
                    'objective': campaign.get('objective'),
                    'created_time': campaign.get('created_time'),
                    'updated_time': campaign.get('updated_time'),
                    'start_time': campaign.get('start_time'),
                    'stop_time': campaign.get('stop_time')
                })
            
            return {
                'account_id': account_id,
                'campaigns': campaign_list,
                'total_count': len(campaign_list)
            }
            
        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")
    
    async def get_ad_sets(self, access_token: str, account_id: str, campaign_id: str = None) -> Dict:
        """Lấy danh sách ad sets"""
        self._get_api(access_token)
        
        try:
            if campaign_id:
                # Get ad sets for specific campaign
                campaign = Campaign(campaign_id)
                ad_sets = campaign.get_ad_sets(fields=[
                    AdSet.Field.id,
                    AdSet.Field.name,
                    AdSet.Field.status,
                    AdSet.Field.campaign_id,
                    AdSet.Field.daily_budget,
                    AdSet.Field.lifetime_budget,
                    AdSet.Field.bid_amount,
                    AdSet.Field.created_time,
                    AdSet.Field.updated_time
                ])
            else:
                # Get all ad sets for account
                account = AdAccount(account_id)
                ad_sets = account.get_ad_sets(fields=[
                    AdSet.Field.id,
                    AdSet.Field.name,
                    AdSet.Field.status,
                    AdSet.Field.campaign_id,
                    AdSet.Field.daily_budget,
                    AdSet.Field.lifetime_budget,
                    AdSet.Field.bid_amount,
                    AdSet.Field.created_time,
                    AdSet.Field.updated_time
                ])
            
            ad_set_list = []
            for ad_set in ad_sets:
                ad_set_list.append({
                    'id': ad_set.get('id'),
                    'name': ad_set.get('name'),
                    'status': ad_set.get('status'),
                    'campaign_id': ad_set.get('campaign_id'),
                    'daily_budget': ad_set.get('daily_budget'),
                    'lifetime_budget': ad_set.get('lifetime_budget'),
                    'bid_amount': ad_set.get('bid_amount'),
                    'created_time': ad_set.get('created_time'),
                    'updated_time': ad_set.get('updated_time')
                })
            
            return {
                'account_id': account_id,
                'campaign_id': campaign_id,
                'ad_sets': ad_set_list,
                'total_count': len(ad_set_list)
            }
            
        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_ads(self, access_token: str, account_id: str, ad_set_id: str = None) -> Dict:
        """Lấy danh sách ads"""
        self._get_api(access_token)

        try:
            if ad_set_id:
                # Get ads for specific ad set
                ad_set = AdSet(ad_set_id)
                ads = ad_set.get_ads(fields=[
                    Ad.Field.id,
                    Ad.Field.name,
                    Ad.Field.status,
                    Ad.Field.adset_id,
                    Ad.Field.campaign_id,
                    Ad.Field.creative,
                    Ad.Field.created_time,
                    Ad.Field.updated_time
                ])
            else:
                # Get all ads for account
                account = AdAccount(account_id)
                ads = account.get_ads(fields=[
                    Ad.Field.id,
                    Ad.Field.name,
                    Ad.Field.status,
                    Ad.Field.adset_id,
                    Ad.Field.campaign_id,
                    Ad.Field.creative,
                    Ad.Field.created_time,
                    Ad.Field.updated_time
                ])

            ads_list = []
            for ad in ads:
                ads_list.append({
                    'id': ad.get('id'),
                    'name': ad.get('name'),
                    'status': ad.get('status'),
                    'adset_id': ad.get('adset_id'),
                    'campaign_id': ad.get('campaign_id'),
                    'creative': ad.get('creative'),
                    'created_time': ad.get('created_time'),
                    'updated_time': ad.get('updated_time')
                })

            return {
                'account_id': account_id,
                'ad_set_id': ad_set_id,
                'ads': ads_list,
                'total_count': len(ads_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_insights(self, access_token: str, account_id: str,
                          level: str = "account", date_preset: str = "last_30d") -> Dict:
        """Lấy insights/reports"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)

            # Define fields for insights
            fields = [
                AdsInsights.Field.impressions,
                AdsInsights.Field.clicks,
                AdsInsights.Field.spend,
                AdsInsights.Field.ctr,
                AdsInsights.Field.cpc,
                AdsInsights.Field.cpm,
                AdsInsights.Field.reach,
                AdsInsights.Field.frequency,
                AdsInsights.Field.actions,
                AdsInsights.Field.cost_per_action_type
            ]

            params = {
                'level': level,
                'date_preset': date_preset
            }

            insights = account.get_insights(fields=fields, params=params)

            insights_list = []
            for insight in insights:
                insights_list.append({
                    'impressions': insight.get('impressions'),
                    'clicks': insight.get('clicks'),
                    'spend': insight.get('spend'),
                    'ctr': insight.get('ctr'),
                    'cpc': insight.get('cpc'),
                    'cpm': insight.get('cpm'),
                    'reach': insight.get('reach'),
                    'frequency': insight.get('frequency'),
                    'actions': insight.get('actions'),
                    'cost_per_action_type': insight.get('cost_per_action_type')
                })

            return {
                'account_id': account_id,
                'level': level,
                'date_preset': date_preset,
                'insights': insights_list,
                'total_count': len(insights_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def create_campaign(self, access_token: str, account_id: str,
                            name: str, objective: str, status: str = "PAUSED") -> Dict:
        """Tạo campaign mới"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)

            campaign = account.create_campaign(
                fields=[],
                params={
                    Campaign.Field.name: name,
                    Campaign.Field.objective: objective,
                    Campaign.Field.status: status,
                }
            )

            return {
                'account_id': account_id,
                'campaign_id': campaign.get('id'),
                'name': name,
                'objective': objective,
                'status': status
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_custom_audiences(self, access_token: str, account_id: str) -> Dict:
        """Lấy danh sách custom audiences"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)
            audiences = account.get_custom_audiences(fields=[
                CustomAudience.Field.id,
                CustomAudience.Field.name,
                CustomAudience.Field.description,
                CustomAudience.Field.approximate_count,
                CustomAudience.Field.subtype,
                CustomAudience.Field.time_created,
                CustomAudience.Field.time_updated
            ])

            audiences_list = []
            for audience in audiences:
                audiences_list.append({
                    'id': audience.get('id'),
                    'name': audience.get('name'),
                    'description': audience.get('description'),
                    'approximate_count': audience.get('approximate_count'),
                    'subtype': audience.get('subtype'),
                    'time_created': audience.get('time_created'),
                    'time_updated': audience.get('time_updated')
                })

            return {
                'account_id': account_id,
                'custom_audiences': audiences_list,
                'total_count': len(audiences_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_ad_creatives(self, access_token: str, account_id: str) -> Dict:
        """Lấy danh sách ad creatives"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)
            creatives = account.get_ad_creatives(fields=[
                AdCreative.Field.id,
                AdCreative.Field.name,
                AdCreative.Field.title,
                AdCreative.Field.body,
                AdCreative.Field.image_url,
                AdCreative.Field.link_url,
                AdCreative.Field.call_to_action_type,
                AdCreative.Field.object_story_spec,
                AdCreative.Field.status
            ])

            creatives_list = []
            for creative in creatives:
                creatives_list.append({
                    'id': creative.get('id'),
                    'name': creative.get('name'),
                    'title': creative.get('title'),
                    'body': creative.get('body'),
                    'image_url': creative.get('image_url'),
                    'link_url': creative.get('link_url'),
                    'call_to_action_type': creative.get('call_to_action_type'),
                    'object_story_spec': creative.get('object_story_spec'),
                    'status': creative.get('status')
                })

            return {
                'account_id': account_id,
                'ad_creatives': creatives_list,
                'total_count': len(creatives_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_ad_images(self, access_token: str, account_id: str) -> Dict:
        """Lấy danh sách ad images"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)

            images = account.get_ad_images(fields=[
                AdImage.Field.hash,
                AdImage.Field.url,
                AdImage.Field.url_128,
                AdImage.Field.width,
                AdImage.Field.height,
                AdImage.Field.original_width,
                AdImage.Field.original_height,
                AdImage.Field.name,
                AdImage.Field.status
            ])

            images_list = []
            for image in images:
                images_list.append({
                    'hash': image.get('hash'),
                    'url': image.get('url'),
                    'url_128': image.get('url_128'),
                    'width': image.get('width'),
                    'height': image.get('height'),
                    'original_width': image.get('original_width'),
                    'original_height': image.get('original_height'),
                    'name': image.get('name'),
                    'status': image.get('status')
                })

            return {
                'account_id': account_id,
                'ad_images': images_list,
                'total_count': len(images_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_pages(self, access_token: str) -> Dict:
        """Lấy danh sách Facebook Pages"""
        self._get_api(access_token)

        try:
            me = User(fbid='me')
            pages = me.get_accounts(fields=[
                Page.Field.id,
                Page.Field.name,
                Page.Field.category,
                Page.Field.category_list,
                Page.Field.link,
                Page.Field.picture,
                Page.Field.cover,
                Page.Field.fan_count,
                Page.Field.access_token,
                Page.Field.tasks
            ])

            pages_list = []
            for page in pages:
                pages_list.append({
                    'id': page.get('id'),
                    'name': page.get('name'),
                    'category': page.get('category'),
                    'category_list': page.get('category_list'),
                    'link': page.get('link'),
                    'picture': page.get('picture'),
                    'cover': page.get('cover'),
                    'fan_count': page.get('fan_count'),
                    'access_token': page.get('access_token'),
                    'tasks': page.get('tasks')
                })

            return {
                'pages': pages_list,
                'total_count': len(pages_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_user_info(self, access_token: str) -> Dict:
        """Lấy thông tin user"""
        self._get_api(access_token)

        try:
            me = User(fbid='me')
            user_info = me.api_get(fields=[
                User.Field.id,
                User.Field.name,
                User.Field.email,
                User.Field.picture,
                User.Field.timezone,
                User.Field.locale,
                User.Field.verified
            ])

            return {
                'user_info': {
                    'id': user_info.get('id'),
                    'name': user_info.get('name'),
                    'email': user_info.get('email'),
                    'picture': user_info.get('picture'),
                    'timezone': user_info.get('timezone'),
                    'locale': user_info.get('locale'),
                    'verified': user_info.get('verified')
                }
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def create_ad_creative(self, access_token: str, account_id: str,
                               name: str, object_story_spec: Dict) -> Dict:
        """Tạo ad creative mới"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)

            creative = account.create_ad_creative(
                fields=[],
                params={
                    AdCreative.Field.name: name,
                    AdCreative.Field.object_story_spec: object_story_spec
                }
            )

            return {
                'account_id': account_id,
                'creative_id': creative.get('id'),
                'name': name,
                'object_story_spec': object_story_spec
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def upload_ad_image(self, access_token: str, account_id: str,
                            image_path: str, image_name: str = None) -> Dict:
        """Upload ad image"""
        self._get_api(access_token)

        try:
            account = AdAccount(account_id)

            # Read image file
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()

            image = account.create_ad_image(
                fields=[],
                params={
                    'bytes': image_data,
                    'name': image_name or os.path.basename(image_path)
                }
            )

            return {
                'account_id': account_id,
                'image_hash': image.get('hash'),
                'image_url': image.get('url'),
                'name': image_name or os.path.basename(image_path)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

# Khởi tạo Facebook Marketing client
fb_client = FacebookMarketingClient()

# Khởi tạo MCP server
mcp = FastMCP("Facebook-Marketing-Server")

@mcp.tool(description="Lấy danh sách ad accounts của user")
async def get_ad_accounts_tool(ctx) -> str:
    """
    Lấy danh sách ad accounts của user
    
    Returns:
        JSON string chứa danh sách ad accounts
    """
    try:
        # Get access token từ environment
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"
        
        await ctx.info("📊 Lấy danh sách ad accounts")
        
        result = await fb_client.get_ad_accounts(access_token)
        
        # Log thông tin cơ bản
        accounts_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {accounts_count} ad accounts")
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ad accounts: {str(e)}")
        return f"❌ Lỗi lấy ad accounts: {str(e)}"

@mcp.tool(description="Lấy danh sách campaigns trong ad account")
async def get_campaigns_tool(
    ctx,
    account_id: str
) -> str:
    """
    Lấy danh sách campaigns trong ad account
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📊 Lấy campaigns cho account: {account_id}")

        result = await fb_client.get_campaigns(access_token, account_id)

        campaigns_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {campaigns_count} campaigns")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy campaigns: {str(e)}")
        return f"❌ Lỗi lấy campaigns: {str(e)}"

@mcp.tool(description="Lấy danh sách ad sets")
async def get_ad_sets_tool(
    ctx,
    account_id: str,
    campaign_id: str = ""
) -> str:
    """
    Lấy danh sách ad sets
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📊 Lấy ad sets cho account: {account_id}")
        if campaign_id:
            await ctx.info(f"🎯 Filter theo campaign: {campaign_id}")

        result = await fb_client.get_ad_sets(access_token, account_id, campaign_id or None)

        ad_sets_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {ad_sets_count} ad sets")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ad sets: {str(e)}")
        return f"❌ Lỗi lấy ad sets: {str(e)}"

@mcp.tool(description="Lấy danh sách ads")
async def get_ads_tool(
    ctx,
    account_id: str,
    ad_set_id: str = ""
) -> str:
    """
    Lấy danh sách ads
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📊 Lấy ads cho account: {account_id}")
        if ad_set_id:
            await ctx.info(f"🎯 Filter theo ad set: {ad_set_id}")

        result = await fb_client.get_ads(access_token, account_id, ad_set_id or None)

        ads_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {ads_count} ads")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ads: {str(e)}")
        return f"❌ Lỗi lấy ads: {str(e)}"

@mcp.tool(description="Lấy insights/reports cho ad account")
async def get_insights_tool(
    ctx,
    account_id: str,
    level: str = "account",
    date_preset: str = "last_30d"
) -> str:
    """
    Lấy insights/reports cho ad account
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📊 Lấy insights cho account: {account_id}")
        await ctx.info(f"📈 Level: {level}, Date preset: {date_preset}")

        result = await fb_client.get_insights(access_token, account_id, level, date_preset)

        insights_count = result.get('total_count', 0)
        await ctx.info(f"✅ Trả về {insights_count} insight records")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy insights: {str(e)}")
        return f"❌ Lỗi lấy insights: {str(e)}"

@mcp.tool(description="Tạo campaign mới")
async def create_campaign_tool(
    ctx,
    account_id: str,
    name: str,
    objective: str,
    status: str = "PAUSED"
) -> str:
    """
    Tạo campaign mới
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"🆕 Tạo campaign mới cho account: {account_id}")
        await ctx.info(f"📝 Tên: {name}, Objective: {objective}")

        result = await fb_client.create_campaign(access_token, account_id, name, objective, status)

        await ctx.info(f"✅ Tạo campaign thành công: {result.get('campaign_id')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo campaign: {str(e)}")
        return f"❌ Lỗi tạo campaign: {str(e)}"

@mcp.tool(description="Lấy danh sách custom audiences")
async def get_custom_audiences_tool(
    ctx,
    account_id: str
) -> str:
    """
    Lấy danh sách custom audiences
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"👥 Lấy custom audiences cho account: {account_id}")

        result = await fb_client.get_custom_audiences(access_token, account_id)

        audiences_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {audiences_count} custom audiences")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy custom audiences: {str(e)}")
        return f"❌ Lỗi lấy custom audiences: {str(e)}"

@mcp.tool(description="Lấy danh sách ad creatives")
async def get_ad_creatives_tool(
    ctx,
    account_id: str
) -> str:
    """
    Lấy danh sách ad creatives
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"🎨 Lấy ad creatives cho account: {account_id}")

        result = await fb_client.get_ad_creatives(access_token, account_id)

        creatives_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {creatives_count} ad creatives")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ad creatives: {str(e)}")
        return f"❌ Lỗi lấy ad creatives: {str(e)}"

@mcp.tool(description="Lấy danh sách ad images")
async def get_ad_images_tool(
    ctx,
    account_id: str
) -> str:
    """
    Lấy danh sách ad images
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"🖼️ Lấy ad images cho account: {account_id}")

        result = await fb_client.get_ad_images(access_token, account_id)

        images_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {images_count} ad images")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ad images: {str(e)}")
        return f"❌ Lỗi lấy ad images: {str(e)}"

@mcp.tool(description="Lấy danh sách Facebook Pages của user")
async def get_pages_tool(ctx) -> str:
    """
    Lấy danh sách Facebook Pages của user
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info("📄 Lấy danh sách Facebook Pages")

        result = await fb_client.get_pages(access_token)

        pages_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {pages_count} pages")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy pages: {str(e)}")
        return f"❌ Lỗi lấy pages: {str(e)}"

@mcp.tool(description="Lấy thông tin user hiện tại")
async def get_user_info_tool(ctx) -> str:
    """
    Lấy thông tin user hiện tại
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info("👤 Lấy thông tin user")

        result = await fb_client.get_user_info(access_token)

        await ctx.info(f"✅ Lấy thông tin user thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy user info: {str(e)}")
        return f"❌ Lỗi lấy user info: {str(e)}"

@mcp.tool(description="Tạo ad creative mới")
async def create_ad_creative_tool(
    ctx,
    account_id: str,
    name: str,
    object_story_spec: str
) -> str:
    """
    Tạo ad creative mới
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        # Parse JSON object story spec
        import json
        object_story_spec_dict = json.loads(object_story_spec)

        await ctx.info(f"🎨 Tạo ad creative mới cho account: {account_id}")
        await ctx.info(f"📝 Tên: {name}")

        result = await fb_client.create_ad_creative(access_token, account_id, name, object_story_spec_dict)

        await ctx.info(f"✅ Tạo ad creative thành công: {result.get('creative_id')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo ad creative: {str(e)}")
        return f"❌ Lỗi tạo ad creative: {str(e)}"

@mcp.tool(description="Upload ad image")
async def upload_ad_image_tool(
    ctx,
    account_id: str,
    image_path: str,
    image_name: str = ""
) -> str:
    """
    Upload ad image
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"🖼️ Upload ad image cho account: {account_id}")
        await ctx.info(f"📁 File: {image_path}")

        result = await fb_client.upload_ad_image(access_token, account_id, image_path, image_name or None)

        await ctx.info(f"✅ Upload image thành công: {result.get('image_hash')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi upload image: {str(e)}")
        return f"❌ Lỗi upload image: {str(e)}"

# Health check endpoint
@mcp.tool(description="Health check cho Facebook Marketing server")
async def health_check() -> str:
    """Health check endpoint"""
    return json.dumps({
        "status": "healthy",
        "service": "Facebook Marketing MCP Server",
        "port": HTTP_PORT,
        "transport": "streamable-http"
    })

def main():
    """Hàm main để chạy server"""
    try:
        print("=" * 70)
        print("📘 Khởi động Facebook Marketing MCP Server")
        print("=" * 70)
        print("� Transport: Streamable HTTP (FastMCP)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print("🔧 Facebook Marketing API: v20.0")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Facebook access token trong Authorization header")
        print()

        print("=" * 70)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
