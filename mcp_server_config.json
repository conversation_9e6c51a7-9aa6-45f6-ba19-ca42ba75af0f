{"mcpServers": {"redai-data-module": {"url": "http://127.0.0.1:8002/mcp", "name": "RedAI Data Module Server", "description": "MCP Server cho Data Module API - Quản lý Media, URL và Statistics", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": false, "description": "Bearer token sẽ đư<PERSON>c cấu hình từ client thông qua tool update_bearer_token"}, "endpoints": {"base": "http://127.0.0.1:8002", "mcp": "http://127.0.0.1:8002/mcp", "health": "http://127.0.0.1:8002/health"}, "tools": [{"name": "delete_media_my-media", "description": "<PERSON><PERSON><PERSON> mềm nhiều media thuộc sở hữu của người dùng hiện tại", "category": "media", "tags": ["User Media"]}, {"name": "post_data_url", "description": "Tạo một URL mới cho người dùng hiện tại", "category": "url", "tags": ["User URL"]}, {"name": "put_data_url", "description": "<PERSON><PERSON><PERSON> nhật thông tin của một URL cụ thể", "category": "url", "tags": ["User URL"]}, {"name": "delete_data_url", "description": "Xóa một URL cụ thể thuộc sở hữu của người dùng hiện tại", "category": "url", "tags": ["User URL"]}, {"name": "update_bearer_token", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t Bearer token cho authentication", "category": "auth", "tags": ["Authentication"]}, {"name": "check_auth_status", "description": "<PERSON><PERSON><PERSON> tra trạng thái authentication hiện tại", "category": "auth", "tags": ["Authentication"]}], "api_info": {"title": "Data Module API", "description": "API documentation for Data Module - <PERSON><PERSON><PERSON><PERSON> lý dữ liệu trong hệ thống bao gồm Media, URL, Knowledge Files và Statistics", "version": "1.0.0", "base_url": "https://api.redai.com", "endpoints_count": 5}, "usage_examples": {"authentication": {"step1": "Gọi tool update_bearer_token với bearer_token", "step2": "Gọi tool check_auth_status để xác nhận", "step3": "Sử dụng các API tools khác"}, "media_management": {"delete_media": "Sử dụng delete_media_my-media với mediaIds array"}, "url_management": {"create_url": "Sử dụng post_data_url với url, title, content", "update_url": "Sử dụng put_data_url với id và thông tin cập nhật", "delete_url": "Sử dụng delete_data_url với id"}}}, "google-sheets": {"url": "http://127.0.0.1:8010/mcp", "name": "Google Sheets MCP Server", "description": "MCP Server cho Google Sheets API - <PERSON><PERSON><PERSON>, đ<PERSON><PERSON>, ghi và quản lý spreadsheets", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": true, "description": "Google OAuth2 access token v<PERSON><PERSON> quyền truy cập Google Sheets và Drive"}, "endpoints": {"base": "http://127.0.0.1:8010", "mcp": "http://127.0.0.1:8010/mcp", "health": "http://127.0.0.1:8010/health"}, "scopes": ["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive.file"], "tools": [{"name": "create_spreadsheet_tool", "description": "Tạo Google Spreadsheet mới", "category": "spreadsheet", "tags": ["Create", "Spreadsheet"]}, {"name": "get_spreadsheet_info_tool", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về spreadsheet", "category": "spreadsheet", "tags": ["Read", "Info"]}, {"name": "read_sheet_data_tool", "description": "<PERSON><PERSON><PERSON> dữ liệu từ sheet", "category": "data", "tags": ["Read", "Data"]}, {"name": "write_sheet_data_tool", "description": "Ghi dữ liệu vào sheet", "category": "data", "tags": ["Write", "Data"]}, {"name": "append_sheet_data_tool", "description": "Thêm dữ liệu vào cuối sheet", "category": "data", "tags": ["Append", "Data"]}, {"name": "clear_sheet_data_tool", "description": "Xóa dữ liệu trong range", "category": "data", "tags": ["Clear", "Data"]}, {"name": "batch_update_sheet_data_tool", "description": "Batch update nhiều ranges cùng lúc", "category": "data", "tags": ["<PERSON><PERSON>", "Update"]}, {"name": "add_sheet_tool", "description": "Thêm sheet mới vào spreadsheet", "category": "sheet", "tags": ["Create", "Sheet"]}, {"name": "delete_sheet_tool", "description": "Xóa sheet khỏi spreadsheet", "category": "sheet", "tags": ["Delete", "Sheet"]}, {"name": "format_cells_tool", "description": "Format cells trong range", "category": "format", "tags": ["Format", "Style"]}], "tags": ["google", "sheets", "spreadsheet", "api"]}, "google-docs": {"url": "http://127.0.0.1:8011/mcp", "name": "Google Docs MCP Server", "description": "MCP Server cho Google Docs API - Tạo, đọc và chỉnh sửa documents", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": true, "description": "Google OAuth2 access token v<PERSON><PERSON> quyền truy cập Google Docs và Drive"}, "endpoints": {"base": "http://127.0.0.1:8011", "mcp": "http://127.0.0.1:8011/mcp", "health": "http://127.0.0.1:8011/health"}, "scopes": ["https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/drive.file"], "tools": [{"name": "create_document_tool", "description": "Tạo Google Document mới", "category": "document", "tags": ["Create", "Document"]}, {"name": "get_document_info_tool", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về document", "category": "document", "tags": ["Read", "Info"]}, {"name": "insert_text_tool", "description": "Chèn text vào document tại vị trí chỉ định", "category": "text", "tags": ["Insert", "Text"]}, {"name": "replace_text_tool", "description": "Thay thế text trong document", "category": "text", "tags": ["Replace", "Text"]}, {"name": "delete_text_tool", "description": "Xóa text trong document", "category": "text", "tags": ["Delete", "Text"]}, {"name": "format_text_tool", "description": "Format text trong document", "category": "format", "tags": ["Format", "Style"]}, {"name": "insert_page_break_tool", "description": "Chèn page break vào document", "category": "structure", "tags": ["Insert", "Page Break"]}, {"name": "insert_table_tool", "description": "Chèn table vào document", "category": "structure", "tags": ["Insert", "Table"]}, {"name": "batch_update_document_tool", "description": "Batch update document v<PERSON><PERSON> requests tùy chỉnh", "category": "batch", "tags": ["<PERSON><PERSON>", "Update"]}], "tags": ["google", "docs", "document", "api"]}, "google-drive": {"url": "http://127.0.0.1:8012/mcp", "name": "Google Drive MCP Server", "description": "MCP Server cho Google Drive API - Quản lý files, folders, permissions và sharing", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": true, "description": "Google OAuth2 access token v<PERSON><PERSON> quyền truy cập Google Drive"}, "endpoints": {"base": "http://127.0.0.1:8012", "mcp": "http://127.0.0.1:8012/mcp", "health": "http://127.0.0.1:8012/health"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"], "tools": [{"name": "list_files_tool", "description": "List files trong Google Drive", "category": "files", "tags": ["List", "Files"]}, {"name": "get_file_info_tool", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về file", "category": "files", "tags": ["Read", "Info"]}, {"name": "create_file_tool", "description": "Tạo file mới trong Google Drive", "category": "files", "tags": ["Create", "File"]}, {"name": "create_folder_tool", "description": "Tạo folder mới trong Google Drive", "category": "folders", "tags": ["Create", "Folder"]}, {"name": "update_file_tool", "description": "<PERSON>ậ<PERSON> nhật file trong Google Drive", "category": "files", "tags": ["Update", "File"]}, {"name": "delete_file_tool", "description": "Xóa file khỏi Google Drive", "category": "files", "tags": ["Delete", "File"]}, {"name": "copy_file_tool", "description": "Copy file trong Google Drive", "category": "files", "tags": ["Copy", "File"]}, {"name": "search_files_tool", "description": "<PERSON><PERSON><PERSON> k<PERSON> files trong Google Drive", "category": "search", "tags": ["Search", "Files"]}], "tags": ["google", "drive", "storage", "api"]}, "google-gmail": {"url": "http://127.0.0.1:8013/mcp", "name": "Google Gmail MCP Server", "description": "MCP Server cho Gmail API - <PERSON><PERSON><PERSON>, nhận và quản lý emails, drafts, labels", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": true, "description": "Google OAuth2 access token v<PERSON><PERSON> quyền truy cập Gmail"}, "endpoints": {"base": "http://127.0.0.1:8013", "mcp": "http://127.0.0.1:8013/mcp", "health": "http://127.0.0.1:8013/health"}, "scopes": ["https://www.googleapis.com/auth/gmail.readonly", "https://www.googleapis.com/auth/gmail.send", "https://www.googleapis.com/auth/gmail.modify", "https://www.googleapis.com/auth/gmail.compose"], "tools": [{"name": "send_email_tool", "description": "<PERSON><PERSON><PERSON> email qua Gmail", "category": "email", "tags": ["Send", "Email"]}, {"name": "list_emails_tool", "description": "List emails trong Gmail", "category": "email", "tags": ["List", "Email"]}, {"name": "get_email_tool", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về email", "category": "email", "tags": ["Read", "Email"]}, {"name": "create_draft_tool", "description": "Tạo draft email", "category": "draft", "tags": ["Create", "Draft"]}, {"name": "send_draft_tool", "description": "Gửi draft email", "category": "draft", "tags": ["Send", "Draft"]}, {"name": "list_labels_tool", "description": "List tất cả labels trong Gmail", "category": "labels", "tags": ["List", "Labels"]}, {"name": "create_label_tool", "description": "Tạo label mới trong Gmail", "category": "labels", "tags": ["Create", "Label"]}, {"name": "modify_email_labels_tool", "description": "Thay đổi labels của email", "category": "labels", "tags": ["Modify", "Labels"]}, {"name": "search_emails_tool", "description": "<PERSON><PERSON><PERSON> emails nâng cao trong Gmail", "category": "search", "tags": ["Search", "Email"]}], "tags": ["google", "gmail", "email", "api"]}, "google-calendar": {"url": "http://127.0.0.1:8014/mcp", "name": "Google Calendar MCP Server", "description": "MCP Server cho Google Calendar API - Quản lý events, calendars và scheduling", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": true, "description": "Google OAuth2 access token v<PERSON><PERSON> quyền truy cập Google Calendar"}, "endpoints": {"base": "http://127.0.0.1:8014", "mcp": "http://127.0.0.1:8014/mcp", "health": "http://127.0.0.1:8014/health"}, "scopes": ["https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/calendar.events", "https://www.googleapis.com/auth/calendar.readonly"], "tools": [{"name": "list_calendars_tool", "description": "List tất cả calendars của user", "category": "calendars", "tags": ["List", "Calendars"]}, {"name": "get_calendar_tool", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về calendar", "category": "calendars", "tags": ["Read", "Calendar"]}, {"name": "create_calendar_tool", "description": "Tạo calendar mới", "category": "calendars", "tags": ["Create", "Calendar"]}, {"name": "update_calendar_tool", "description": "Update calendar", "category": "calendars", "tags": ["Update", "Calendar"]}, {"name": "delete_calendar_tool", "description": "Xóa calendar", "category": "calendars", "tags": ["Delete", "Calendar"]}, {"name": "list_events_tool", "description": "List events trong calendar", "category": "events", "tags": ["List", "Events"]}, {"name": "get_event_tool", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về event", "category": "events", "tags": ["Read", "Event"]}, {"name": "create_event_tool", "description": "Tạo event mới", "category": "events", "tags": ["Create", "Event"]}, {"name": "update_event_tool", "description": "Update event", "category": "events", "tags": ["Update", "Event"]}, {"name": "delete_event_tool", "description": "Xóa event", "category": "events", "tags": ["Delete", "Event"]}, {"name": "quick_add_event_tool", "description": "Tạo event n<PERSON>h từ text tự nhiên", "category": "events", "tags": ["Quick", "Create", "Event"]}, {"name": "freebusy_query_tool", "description": "Query freebusy information cho calendars", "category": "freebusy", "tags": ["Query", "Freebusy"]}], "tags": ["google", "calendar", "events", "scheduling", "api"]}, "google-ads": {"url": "http://127.0.0.1:8020/mcp", "name": "Google Ads MCP Server", "description": "MCP Server cho Google Ads API - Quản lý campaigns, ad groups, keywords, ads và bidding strategies", "version": "1.0.0", "transport": "streamable-http", "timeout": 30000, "retries": 3, "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "authentication": {"type": "bearer", "required": true, "description": "Google OAuth2 access token v<PERSON><PERSON> quyền truy cập Google Ads API"}, "endpoints": {"base": "http://127.0.0.1:8020", "mcp": "http://127.0.0.1:8020/mcp", "health": "http://127.0.0.1:8020/health", "sse_fallback": "http://127.0.0.1:8120/sse"}, "scopes": ["https://www.googleapis.com/auth/adwords"], "tools": [{"name": "get_campaigns_tool", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch campaigns trong Google Ads", "category": "campaign", "tags": ["Read", "Campaign"]}, {"name": "create_campaign_tool", "description": "Tạo campaign mới trong Google Ads", "category": "campaign", "tags": ["Create", "Campaign"]}, {"name": "get_ad_groups_tool", "description": "Lấy danh sách ad groups", "category": "adgroup", "tags": ["Read", "AdGroup"]}, {"name": "create_ad_group_tool", "description": "Tạo ad group mới trong campaign", "category": "adgroup", "tags": ["Create", "AdGroup"]}, {"name": "get_keywords_tool", "description": "<PERSON><PERSON><PERSON> danh sách keywords", "category": "keyword", "tags": ["Read", "Keyword"]}, {"name": "create_keyword_tool", "description": "Tạo keyword mới trong ad group", "category": "keyword", "tags": ["Create", "Keyword"]}, {"name": "get_keyword_ideas_tool", "description": "L<PERSON>y keyword ideas từ Keyword Planner", "category": "keyword", "tags": ["Research", "Keyword", "Ideas"]}, {"name": "get_ads_tool", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch ads", "category": "ad", "tags": ["Read", "Ad"]}, {"name": "create_responsive_search_ad_tool", "description": "Tạo Responsive Search Ad", "category": "ad", "tags": ["Create", "Ad", "RSA"]}, {"name": "get_campaign_performance_tool", "description": "Lấy performance report cho campaigns", "category": "report", "tags": ["Report", "Performance", "Analytics"]}, {"name": "get_recommendations_tool", "description": "<PERSON><PERSON><PERSON> recommendations để tối <PERSON>u hóa campaigns", "category": "optimization", "tags": ["Optimization", "Recommendations"]}, {"name": "get_bidding_strategies_tool", "description": "<PERSON><PERSON><PERSON> danh sách bidding strategies", "category": "bidding", "tags": ["Read", "Bidding", "Strategy"]}, {"name": "create_target_cpa_bidding_strategy_tool", "description": "Tạo Target CPA bidding strategy", "category": "bidding", "tags": ["Create", "Bidding", "CPA"]}, {"name": "update_campaign_tool", "description": "<PERSON><PERSON><PERSON> campaign", "category": "campaign", "tags": ["Update", "Campaign"]}, {"name": "remove_campaign_tool", "description": "Xóa campaign", "category": "campaign", "tags": ["Delete", "Campaign"]}, {"name": "pause_ad_tool", "description": "<PERSON><PERSON><PERSON> dừng ad", "category": "ad", "tags": ["Update", "Ad", "Pause"]}, {"name": "add_sitelinks_tool", "description": "Thêm sitelink extensions", "category": "extension", "tags": ["Create", "Extension", "Sitelink"]}, {"name": "add_call_extensions_tool", "description": "Thêm call extensions", "category": "extension", "tags": ["Create", "Extension", "Call"]}, {"name": "add_performance_max_campaign_tool", "description": "Tạo Performance Max campaign", "category": "campaign", "tags": ["Create", "Campaign", "Performance Max", "Advanced"]}, {"name": "add_smart_campaign_tool", "description": "Tạo Smart campaign", "category": "campaign", "tags": ["Create", "Campaign", "Smart", "Advanced"]}], "tags": ["google", "ads", "advertising", "ppc", "sem", "extensions", "advanced"]}}, "client_config": {"default_timeout": 30000, "retry_attempts": 3, "retry_delay": 1000, "connection_pool_size": 10, "keep_alive": true, "user_agent": "FastMCP-Client/2.0", "headers": {"Content-Type": "application/json", "Accept": "application/json"}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "mcp_client.log", "console": true}, "environment": {"development": {"server_url": "http://127.0.0.1:8002/mcp", "debug": true, "log_level": "DEBUG"}, "production": {"server_url": "https://api.redai.com/mcp", "debug": false, "log_level": "INFO", "ssl_verify": true}}, "metadata": {"created": "2025-01-16", "version": "1.0.0", "author": "RedAI Development Team", "description": "<PERSON><PERSON><PERSON> hình kết nối MCP cho RedAI Data Module Server", "last_updated": "2025-01-16T11:06:00Z", "schema_version": "1.0"}}